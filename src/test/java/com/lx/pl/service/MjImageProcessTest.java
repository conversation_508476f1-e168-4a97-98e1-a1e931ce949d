//package com.lx.pl.service;
//
//import com.lx.pl.dto.mq.MjImageProcessResultVo;
//import com.lx.pl.dto.mq.MjImageProcessVo;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * MJ图片处理测试
// */
//@SpringBootTest
//public class MjImageProcessTest {
//
//    @Test
//    public void testMjImageProcessVo() {
//        // 测试图片处理消息体
//        MjImageProcessVo processVo = new MjImageProcessVo();
//        processVo.setJobId("test-job-123");
//        processVo.setLoginName("<EMAIL>");
//        processVo.setMarkId("test-mark-456");
//
//        List<MjImageProcessVo.ImageInfo> imageInfos = new ArrayList<>();
//        MjImageProcessVo.ImageInfo imageInfo = new MjImageProcessVo.ImageInfo();
//        imageInfo.setOriginalUrl("https://example.com/original.jpg");
//        imageInfo.setFileName("test_image.jpg");
//        imageInfo.setWidth(1024);
//        imageInfo.setHeight(1024);
//        imageInfos.add(imageInfo);
//
//        processVo.setImageInfos(imageInfos);
//
//        System.out.println("MjImageProcessVo created: " + processVo.toString());
//    }
//
//    @Test
//    public void testMjImageProcessResultVo() {
//        // 测试图片处理结果消息体
//        MjImageProcessResultVo resultVo = new MjImageProcessResultVo();
//        resultVo.setJobId("test-job-123");
//        resultVo.setLoginName("<EMAIL>");
//        resultVo.setMarkId("test-mark-456");
//        resultVo.setStatus("SUCCESS");
//
//        List<MjImageProcessResultVo.ProcessedImageInfo> processedImages = new ArrayList<>();
//        MjImageProcessResultVo.ProcessedImageInfo processedImage = new MjImageProcessResultVo.ProcessedImageInfo();
//        processedImage.setOriginalUrl("https://example.com/original.jpg");
//        processedImage.setThumbnailUrl("https://example.com/thumbnail.jpg");
//        processedImage.setHighThumbnailUrl("https://example.com/high_thumbnail.jpg");
//        processedImage.setMiniThumbnailUrl("https://example.com/mini_thumbnail.jpg");
//        processedImage.setHighMiniUrl("https://example.com/high_mini.jpg");
//        processedImage.setProcessStatus("SUCCESS");
//        processedImages.add(processedImage);
//
//        resultVo.setProcessedImages(processedImages);
//
//        System.out.println("MjImageProcessResultVo created: " + resultVo.toString());
//    }
//}
