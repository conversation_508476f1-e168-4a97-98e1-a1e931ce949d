//package com.lx.pl.util;
//
//import com.lx.pl.dto.GenGenericPara;
//import com.lx.pl.dto.Resolution;
//import org.junit.jupiter.api.Test;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * AspectRatioUtils测试类
// */
//public class AspectRatioUtilsTest {
//
//    @Test
//    public void testGetAspectRatio() {
//        // 测试标准分辨率
//        assertEquals("1024 x 1024", AspectRatioUtils.getAspectRatio(1024, 1024));
//        assertEquals("1344 x 768", AspectRatioUtils.getAspectRatio(1344, 768));
//        assertEquals("768 x 1344", AspectRatioUtils.getAspectRatio(768, 1344));
//        assertEquals("1536 x 640", AspectRatioUtils.getAspectRatio(1536, 640));
//
//        // 测试非标准分辨率
//        assertEquals("800 x 600", AspectRatioUtils.getAspectRatio(800, 600));
//        assertEquals("1920 x 1080", AspectRatioUtils.getAspectRatio(1920, 1080));
//    }
//
//    @Test
//    public void testGetAspectRatioLabel() {
//        // 测试标准比例标签
//        assertEquals("1:1", AspectRatioUtils.getAspectRatioLabel(1024, 1024));
//        assertEquals("16:9", AspectRatioUtils.getAspectRatioLabel(1344, 768));
//        assertEquals("9:16", AspectRatioUtils.getAspectRatioLabel(768, 1344));
//        assertEquals("21:9", AspectRatioUtils.getAspectRatioLabel(1536, 640));
//        assertEquals("3:2", AspectRatioUtils.getAspectRatioLabel(1216, 832));
//
//        // 测试非标准比例
//        assertEquals("4:3", AspectRatioUtils.getAspectRatioLabel(800, 600));
//        assertEquals("16:9", AspectRatioUtils.getAspectRatioLabel(1920, 1080));
//    }
//
//    @Test
//    public void testGetAspectRatioFromGenGenericPara() {
//        // 测试GenGenericPara参数
//        GenGenericPara genPara = new GenGenericPara();
//        Resolution resolution = new Resolution();
//
//        // 测试1:1比例
//        resolution.setWidth(1024);
//        resolution.setHeight(1024);
//        genPara.setResolution(resolution);
//        assertEquals("1024 x 1024", AspectRatioUtils.getAspectRatio(genPara));
//        assertEquals("1:1", AspectRatioUtils.getAspectRatioLabel(genPara));
//
//        // 测试16:9比例
//        resolution.setWidth(1344);
//        resolution.setHeight(768);
//        genPara.setResolution(resolution);
//        assertEquals("1344 x 768", AspectRatioUtils.getAspectRatio(genPara));
//        assertEquals("16:9", AspectRatioUtils.getAspectRatioLabel(genPara));
//
//        // 测试9:16比例
//        resolution.setWidth(768);
//        resolution.setHeight(1344);
//        genPara.setResolution(resolution);
//        assertEquals("768 x 1344", AspectRatioUtils.getAspectRatio(genPara));
//        assertEquals("9:16", AspectRatioUtils.getAspectRatioLabel(genPara));
//    }
//
//    @Test
//    public void testIsSupportedResolution() {
//        // 测试支持的分辨率
//        assertTrue(AspectRatioUtils.isSupportedResolution(1024, 1024));
//        assertTrue(AspectRatioUtils.isSupportedResolution(1344, 768));
//        assertTrue(AspectRatioUtils.isSupportedResolution(768, 1344));
//        assertTrue(AspectRatioUtils.isSupportedResolution(1536, 640));
//
//        // 测试不支持的分辨率
//        assertFalse(AspectRatioUtils.isSupportedResolution(800, 600));
//        assertFalse(AspectRatioUtils.isSupportedResolution(1920, 1080));
//    }
//
//    @Test
//    public void testGetClosestSupportedResolution() {
//        // 测试获取最接近的支持分辨率
//        AspectRatioUtils.ShapeInfo closest1 = AspectRatioUtils.getClosestSupportedResolution(1000, 1000);
//        assertEquals(1024, closest1.width);
//        assertEquals(1024, closest1.height);
//        assertEquals("1:1", closest1.label);
//
//        // 测试16:9比例
//        AspectRatioUtils.ShapeInfo closest2 = AspectRatioUtils.getClosestSupportedResolution(1920, 1080);
//        assertEquals(1344, closest2.width);
//        assertEquals(768, closest2.height);
//        assertEquals("16:9", closest2.label);
//
//        // 测试竖屏比例
//        AspectRatioUtils.ShapeInfo closest3 = AspectRatioUtils.getClosestSupportedResolution(1080, 1920);
//        assertEquals(768, closest3.width);
//        assertEquals(1344, closest3.height);
//        assertEquals("9:16", closest3.label);
//    }
//
//    @Test
//    public void testNullParameters() {
//        // 测试null参数
//        assertEquals("1024 x 1024", AspectRatioUtils.getAspectRatio(null));
//        assertEquals("1:1", AspectRatioUtils.getAspectRatioLabel(null));
//
//        // 测试null resolution
//        GenGenericPara genPara = new GenGenericPara();
//        genPara.setResolution(null);
//        assertEquals("1024 x 1024", AspectRatioUtils.getAspectRatio(genPara));
//        assertEquals("1:1", AspectRatioUtils.getAspectRatioLabel(genPara));
//    }
//
//    @Test
//    public void testGetAllSupportedResolutions() {
//        AspectRatioUtils.ShapeInfo[] shapes = AspectRatioUtils.getAllSupportedResolutions();
//        assertNotNull(shapes);
//        assertEquals(17, shapes.length); // 应该有17个预定义的分辨率
//
//        // 验证第一个是1:1
//        assertEquals(1024, shapes[0].width);
//        assertEquals(1024, shapes[0].height);
//        assertEquals("1:1", shapes[0].label);
//        assertEquals("1024 x 1024", shapes[0].value);
//        assertEquals("SDXL", shapes[0].baseVersion);
//    }
//
//    @Test
//    public void testSpecialRatios() {
//        // 测试特殊比例
//        assertEquals("21:9", AspectRatioUtils.getAspectRatioLabel(1536, 640));
//        assertEquals("9:21", AspectRatioUtils.getAspectRatioLabel(640, 1536));
//        assertEquals("19:9", AspectRatioUtils.getAspectRatioLabel(1472, 704));
//        assertEquals("9:19", AspectRatioUtils.getAspectRatioLabel(704, 1472));
//        assertEquals("6:5", AspectRatioUtils.getAspectRatioLabel(1088, 960));
//        assertEquals("5:6", AspectRatioUtils.getAspectRatioLabel(960, 1088));
//    }
//}
