package com.lx.pl.schedule;

import com.lx.pl.schedule.entity.*;
import com.lx.pl.schedule.service.FlightScheduleService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 航线调度算法测试类
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class FlightScheduleTest {
    
    @Autowired
    private FlightScheduleService flightScheduleService;
    
    @Test
    public void testSingleRouteSchedule() {
        // 创建测试数据
        Route route = createTestRoute();
        List<Pilot> pilots = createTestPilots();
        List<ScheduleConstraint> constraints = createTestConstraints();
        List<PilotPartnership> partnerships = createTestPartnerships();
        
        // 执行调度
        ScheduleResult result = flightScheduleService.scheduleRoute(route, pilots, constraints, partnerships);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isValid());
        assertEquals(2, result.getAssignedPilotIds().size());
        assertTrue(result.getScheduleScore() > 0);
        
        System.out.println("调度结果:");
        System.out.println("航线: " + result.getRouteName());
        System.out.println("分配飞行员: " + String.join(", ", result.getAssignedPilotNames()));
        System.out.println("调度评分: " + result.getScheduleScore());
    }
    
    @Test
    public void testMultipleRoutesSchedule() {
        // 创建测试数据
        List<Route> routes = createMultipleTestRoutes();
        List<Pilot> pilots = createTestPilots();
        List<ScheduleConstraint> constraints = createTestConstraints();
        List<PilotPartnership> partnerships = createTestPartnerships();
        
        // 执行批量调度
        List<ScheduleResult> results = flightScheduleService.scheduleMultipleRoutes(
                routes, pilots, constraints, partnerships);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(3, results.size());
        
        // 检查是否有成功调度的航线
        long successCount = results.stream().filter(ScheduleResult::isValid).count();
        assertTrue(successCount > 0);
        
        System.out.println("\n批量调度结果:");
        for (ScheduleResult result : results) {
            System.out.println("航线: " + result.getRouteName() + 
                             ", 状态: " + result.getStatus() + 
                             ", 评分: " + result.getScheduleScore());
            if (result.getAssignedPilotNames() != null) {
                System.out.println("  分配飞行员: " + String.join(", ", result.getAssignedPilotNames()));
            }
        }
    }
    
    @Test
    public void testConstraintValidation() {
        // 测试强制约束验证
        Route hardRoute = createHardRoute();
        List<Pilot> pilots = createTestPilots();
        List<ScheduleConstraint> constraints = createTestConstraints();
        List<PilotPartnership> partnerships = createTestPartnerships();
        
        ScheduleResult result = flightScheduleService.scheduleRoute(hardRoute, pilots, constraints, partnerships);
        
        // 困难航线应该只分配给有足够经验的机长
        if (result.isValid() && result.getAssignedPilotIds() != null) {
            for (String pilotId : result.getAssignedPilotIds()) {
                Pilot assignedPilot = pilots.stream()
                        .filter(p -> p.getPilotId().equals(pilotId))
                        .findFirst()
                        .orElse(null);
                
                assertNotNull(assignedPilot);
                // 困难航线需要机长
                if (hardRoute.getMinimumRank() == Pilot.PilotRank.CAPTAIN) {
                    assertEquals(Pilot.PilotRank.CAPTAIN, assignedPilot.getRank());
                }
            }
        }
        
        System.out.println("\n约束验证测试:");
        System.out.println("困难航线调度结果: " + (result.isValid() ? "成功" : "失败"));
        if (result.getAssignedPilotNames() != null) {
            System.out.println("分配的飞行员: " + String.join(", ", result.getAssignedPilotNames()));
        }
    }
    
    @Test
    public void testPartnershipOptimization() {
        // 测试搭档优化
        List<Pilot> candidatePilots = createTestPilots();
        List<PilotPartnership> partnerships = createTestPartnerships();
        
        List<Pilot> bestCombination = flightScheduleService.getBestPilotCombination(
                candidatePilots, 2, partnerships);
        
        assertNotNull(bestCombination);
        assertEquals(2, bestCombination.size());
        
        System.out.println("\n搭档优化测试:");
        System.out.println("最佳搭档组合: " + 
                bestCombination.stream()
                        .map(Pilot::getPilotName)
                        .reduce((a, b) -> a + " & " + b)
                        .orElse("无"));
    }
    
    // 创建测试数据的辅助方法
    private Route createTestRoute() {
        Route route = new Route();
        route.setRouteId("TEST_R001");
        route.setRouteName("测试航线A");
        route.setDifficulty(Route.RouteDifficulty.MEDIUM);
        route.setRequiredPilots(2);
        route.setMinimumRank(Pilot.PilotRank.FIRST_OFFICER);
        route.setStatus(Route.RouteStatus.ACTIVE);
        route.setScheduledDepartureTime(LocalDateTime.now().plusHours(2));
        return route;
    }
    
    private Route createHardRoute() {
        Route route = new Route();
        route.setRouteId("TEST_R_HARD");
        route.setRouteName("困难测试航线");
        route.setDifficulty(Route.RouteDifficulty.HARD);
        route.setRequiredPilots(2);
        route.setMinimumRank(Pilot.PilotRank.CAPTAIN);
        route.setStatus(Route.RouteStatus.ACTIVE);
        route.setScheduledDepartureTime(LocalDateTime.now().plusHours(2));
        return route;
    }
    
    private List<Route> createMultipleTestRoutes() {
        List<Route> routes = new ArrayList<>();
        
        Route route1 = createTestRoute();
        route1.setRouteId("TEST_R001");
        route1.setRouteName("测试航线A");
        routes.add(route1);
        
        Route route2 = createTestRoute();
        route2.setRouteId("TEST_R002");
        route2.setRouteName("测试航线B");
        route2.setScheduledDepartureTime(LocalDateTime.now().plusHours(4));
        routes.add(route2);
        
        Route route3 = createHardRoute();
        route3.setScheduledDepartureTime(LocalDateTime.now().plusHours(6));
        routes.add(route3);
        
        return routes;
    }
    
    private List<Pilot> createTestPilots() {
        List<Pilot> pilots = new ArrayList<>();
        
        Pilot pilot1 = new Pilot();
        pilot1.setPilotId("TEST_P001");
        pilot1.setPilotName("测试张三");
        pilot1.setRank(Pilot.PilotRank.CAPTAIN);
        pilot1.setStatus(Pilot.PilotStatus.AVAILABLE);
        pilot1.setFlightHours(1500);
        pilot1.setConsecutiveWorkDays(2);
        pilot1.setMaxConsecutiveWorkDays(5);
        pilots.add(pilot1);
        
        Pilot pilot2 = new Pilot();
        pilot2.setPilotId("TEST_P002");
        pilot2.setPilotName("测试李四");
        pilot2.setRank(Pilot.PilotRank.FIRST_OFFICER);
        pilot2.setStatus(Pilot.PilotStatus.AVAILABLE);
        pilot2.setFlightHours(800);
        pilot2.setConsecutiveWorkDays(1);
        pilot2.setMaxConsecutiveWorkDays(4);
        pilots.add(pilot2);
        
        Pilot pilot3 = new Pilot();
        pilot3.setPilotId("TEST_P003");
        pilot3.setPilotName("测试王五");
        pilot3.setRank(Pilot.PilotRank.FIRST_OFFICER);
        pilot3.setStatus(Pilot.PilotStatus.AVAILABLE);
        pilot3.setFlightHours(600);
        pilot3.setConsecutiveWorkDays(0);
        pilot3.setMaxConsecutiveWorkDays(4);
        pilots.add(pilot3);
        
        Pilot pilot4 = new Pilot();
        pilot4.setPilotId("TEST_P004");
        pilot4.setPilotName("测试赵六");
        pilot4.setRank(Pilot.PilotRank.CAPTAIN);
        pilot4.setStatus(Pilot.PilotStatus.AVAILABLE);
        pilot4.setFlightHours(2000);
        pilot4.setConsecutiveWorkDays(3);
        pilot4.setMaxConsecutiveWorkDays(5);
        pilots.add(pilot4);
        
        return pilots;
    }
    
    private List<ScheduleConstraint> createTestConstraints() {
        List<ScheduleConstraint> constraints = new ArrayList<>();
        
        ScheduleConstraint constraint1 = new ScheduleConstraint();
        constraint1.setConstraintId("TEST_C001");
        constraint1.setConstraintName("测试职级要求");
        constraint1.setType(ScheduleConstraint.ConstraintType.HARD);
        constraint1.setCategory(ScheduleConstraint.ConstraintCategory.PILOT_QUALIFICATION);
        constraint1.setEnabled(true);
        constraints.add(constraint1);
        
        ScheduleConstraint constraint2 = new ScheduleConstraint();
        constraint2.setConstraintId("TEST_C002");
        constraint2.setConstraintName("测试工作时间");
        constraint2.setType(ScheduleConstraint.ConstraintType.HARD);
        constraint2.setCategory(ScheduleConstraint.ConstraintCategory.WORK_TIME);
        constraint2.setEnabled(true);
        constraints.add(constraint2);
        
        return constraints;
    }
    
    private List<PilotPartnership> createTestPartnerships() {
        List<PilotPartnership> partnerships = new ArrayList<>();
        
        // 张三和李四合作过最多次
        PilotPartnership partnership1 = new PilotPartnership("TEST_P001", "测试张三", "TEST_P002", "测试李四");
        partnership1.setCooperationCount(15);
        partnership1.setCooperationScore(8.5);
        partnerships.add(partnership1);
        
        // 张三和王五合作过
        PilotPartnership partnership2 = new PilotPartnership("TEST_P001", "测试张三", "TEST_P003", "测试王五");
        partnership2.setCooperationCount(8);
        partnership2.setCooperationScore(7.0);
        partnerships.add(partnership2);
        
        return partnerships;
    }
}
