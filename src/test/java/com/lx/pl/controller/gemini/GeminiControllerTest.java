package com.lx.pl.controller.gemini;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Gemini控制器测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureTestMvc
public class GeminiControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试Nano Banana图像生成接口
     */
    @Test
    public void testCreateNanoBananaTask() throws Exception {
        GeminiController.NanoBananaParams params = new GeminiController.NanoBananaParams();
        params.setPrompt("A beautiful sunset over the ocean");
        params.setNumImages(1);
        params.setOutputFormat("jpeg");
        params.setSyncMode(false);

        mockMvc.perform(post("/api/gemini/nano-banana")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(params))
                .header("Authorization", "Bearer test_token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试同步生成图像接口
     */
    @Test
    public void testGenerateImageSync() throws Exception {
        GeminiController.NanoBananaParams params = new GeminiController.NanoBananaParams();
        params.setPrompt("A cute cat sitting on a windowsill");
        params.setNumImages(1);
        params.setOutputFormat("jpeg");
        params.setSyncMode(true);

        mockMvc.perform(post("/api/gemini/generate-sync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(params))
                .header("Authorization", "Bearer test_token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试兼容接口
     */
    @Test
    public void testCreateCompatible() throws Exception {
        GenGenericPara genParameters = new GenGenericPara();
        genParameters.setPrompt("A futuristic cityscape at night");
        genParameters.setNegative_prompt("");
        genParameters.setModel_id("gemini-nano-banana-001");
        
        Resolution resolution = new Resolution();
        resolution.setWidth(1024);
        resolution.setHeight(1024);
        resolution.setBatch_size(1);
        genParameters.setResolution(resolution);

        mockMvc.perform(post("/api/gemini/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(genParameters))
                .header("Authorization", "Bearer test_token")
                .header("Platform", "web"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.markId").exists())
                .andExpect(jsonPath("$.data.requestId").exists());
    }

    /**
     * 测试获取任务结果接口
     */
    @Test
    public void testGetTaskResult() throws Exception {
        String requestId = "test-request-id-123";

        mockMvc.perform(get("/api/gemini/result/" + requestId)
                .header("Authorization", "Bearer test_token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.images").exists())
                .andExpect(jsonPath("$.data.description").exists());
    }

    /**
     * 测试获取任务状态接口
     */
    @Test
    public void testGetTaskStatus() throws Exception {
        String requestId = "test-request-id-123";

        mockMvc.perform(get("/api/gemini/status/" + requestId)
                .header("Authorization", "Bearer test_token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
