package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.mapper.SubscriptionCurrentMapper;
import com.lx.pl.pay.common.service.impl.SubscriptionCurrentServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lx.pl.pay.common.service.impl.SubscriptionCurrentServiceImpl.CACHE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/7/30
 * @description 用户会员等级信息缓存Service
 */
@Slf4j
@Service
public class VipTypeCacheService {
    @Resource
    private RedisService redisService;
    @Resource(name = "logicObjectMapper")
    private ObjectMapper logicObjectMapper;
    @Resource
    private SubscriptionCurrentMapper subscriptionCurrentMapper;

    public final Cache<Long, String> userVipTypeCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    public String getVipTypeFromLocalCache(Long userId) {
        return userVipTypeCache.getIfPresent(userId);
    }

    public void addLocalCache(Long userId, String vipType) {
        userVipTypeCache.put(userId, vipType);
    }

    public Map<Long, String> getVipType(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }

        Map<Long, String> result = new HashMap<>();

        //本地缓存未找到会员信息的用户
        List<Long> localCacheNotExist = new ArrayList<>();
        userIds.forEach(userId -> {
            String localCacheVipType = getVipTypeFromLocalCache(userId);
            if (localCacheVipType != null) {
                result.put(userId, localCacheVipType);
            } else {
                localCacheNotExist.add(userId);
            }
        });

        if (CollectionUtils.isEmpty(localCacheNotExist)) {
            return result;
        }

        //从redis中获取
        Map<Long, String> redisCacheDataMap = getVipTypeFromRedis(localCacheNotExist);
        //redis中未找到会员信息的用户id
        List<Long> redisCacheNotExist = new ArrayList<>();
        //放入本地缓存
        localCacheNotExist.forEach(userId -> {
            String redisCacheVipType = redisCacheDataMap.get(userId);
            if (StringUtils.isBlank(redisCacheVipType)) {
                redisCacheNotExist.add(userId);
                return;
            }
            result.put(userId, redisCacheVipType);
            addLocalCache(userId, redisCacheVipType);
        });

        if (CollectionUtils.isEmpty(redisCacheNotExist)) {
            return result;
        }

        //从数据库中获取
        Map<Long, String> dbData = getVipTypeFromDB(redisCacheNotExist);
        //放入本地缓存
        redisCacheNotExist.forEach(userId -> {
            String dbVipType = dbData.get(userId);
            result.put(userId, dbVipType);
            addLocalCache(userId, dbVipType);
        });

        return result;
    }

    public Map<Long, String> getVipTypeFromRedis(List<Long> userIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }

        List<String> cacheKeys = userIds.stream().map(userId -> CACHE_KEY_PREFIX + userId).collect(Collectors.toList());
        Map<String, Object> cachedDataMap = redisService.getKeys(cacheKeys);
        if (CollectionUtils.isEmpty(cachedDataMap)) {
            return result;
        }

        userIds.forEach(userId -> {
            Object cachedData = cachedDataMap.get(CACHE_KEY_PREFIX + userId);
            if (cachedData != null) {
                try {
                    SubscriptionCurrent cachedSubscription = logicObjectMapper.readValue(logicObjectMapper.writeValueAsString(cachedData), SubscriptionCurrent.class);
                    result.put(userId, cachedSubscription.getPlanLevel());
                } catch (JsonProcessingException e) {
                    log.error("获取用户当前订阅信息缓存异常, userId: {}", userId, e);
                }
            }
        });

        return result;
    }

    public Map<Long, String> getVipTypeFromDB(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        Map<Long, String> resultMap = new HashMap<>();
        long currentTime = Instant.now().getEpochSecond();
        List<SubscriptionCurrent> subscriptionCurrentList = subscriptionCurrentMapper.selectList(
                new LambdaQueryWrapper<>(SubscriptionCurrent.class)
                        .in(SubscriptionCurrent::getUserId, userIds)
                        .eq(SubscriptionCurrent::getInvalid, 0)
                        .le(SubscriptionCurrent::getVipBeginTime, currentTime)
                        .ge(SubscriptionCurrent::getVipEndTime, currentTime)
                        .select(SubscriptionCurrent::getUserId, SubscriptionCurrent::getPlanLevel, SubscriptionCurrent::getPriceInterval)
        );

        if (!CollectionUtils.isEmpty(subscriptionCurrentList)) {
            Map<Long, List<SubscriptionCurrent>> subscriptionMap = subscriptionCurrentList.stream()
                    .collect(Collectors.groupingBy(SubscriptionCurrent::getUserId));

            subscriptionMap.forEach((userId, subscriptions) -> resultMap.put(userId, SubscriptionCurrentServiceImpl.getHighestVipSubscription(subscriptions).getPlanLevel()));
        }

        if (resultMap.size() != userIds.size()) {
            userIds.stream().filter(userId -> !resultMap.containsKey(userId)).forEach(userId -> resultMap.put(userId, "basic"));
        }

        return resultMap;
    }
}
