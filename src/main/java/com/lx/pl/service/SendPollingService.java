package com.lx.pl.service;

import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.mq.producer.NormalMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 统一任务状态轮询服务
 * 支持MJ和Flux两种任务类型的轮询
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SendPollingService {

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Value("${rocketmq.midjourney.polling.topic:tp_midjourney_polling_test}")
    private String pollingTopic;

    @Value("${rocketmq.midjourney.polling.tag:tag_midjourney_polling_test}")
    private String pollingTag;

    /**
     * 发送轮询延时消息
     */
    public void sendPollingMessage(TaskPollingVo pollingVo, long delaySeconds) {
        try {
            CommonMqMessage<TaskPollingVo> mqMessage = new RMqMessage<>(
                    pollingTopic,
                    pollingTag,
                    pollingVo.getJobId() + "_" + pollingVo.getCurrentAttempt()
            );
            mqMessage.setMessage(pollingVo);

            normalMessageProducer.syncDelaySend(mqMessage, delaySeconds);

            log.debug("Sent {} polling message for taskId: {}, attempt: {}, delay: {}s",
                    pollingVo.getTaskType(), pollingVo.getJobId(), pollingVo.getCurrentAttempt(), delaySeconds);
        } catch (Exception e) {
            log.error("Failed to send {} polling message for taskId: {}",
                    pollingVo.getTaskType(), pollingVo.getJobId(), e);
        }
    }

    /**
     * 安排下一次轮询
     */
    public void scheduleNextPolling(TaskPollingVo pollingVo, int nextAttempt) {

        // 创建下一次轮询消息
        TaskPollingVo nextPollingVo = new TaskPollingVo();
        nextPollingVo.setTaskType(pollingVo.getTaskType());
        nextPollingVo.setJobId(pollingVo.getJobId());
        nextPollingVo.setLoginName(pollingVo.getLoginName());
        nextPollingVo.setCurrentAttempt(nextAttempt);
        nextPollingVo.setMaxAttempts(pollingVo.getMaxAttempts());
        nextPollingVo.setPollingInterval(pollingVo.getPollingInterval());
        nextPollingVo.setCreateTimestamp(pollingVo.getCreateTimestamp());
        nextPollingVo.setPollingUrl(pollingVo.getPollingUrl());

        // 发送延时消息，延时时间为轮询间隔（毫秒转秒）
        long delaySeconds = pollingVo.getPollingInterval() / 1000;
        sendPollingMessage(nextPollingVo, delaySeconds);

    }
}
