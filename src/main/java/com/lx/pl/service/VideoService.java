package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.*;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 视频生成服务
 */
@Slf4j
@Service
public class VideoService {

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private LoadBalanceService loadBalanceService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private GenService genService;

    @Value("${picflow.modelId}")
    private String picflowModelId;

    @Value("${callback_url}")
    String callback_url;

    /**
     * 生成视频 - 遵循现有图片生成流程
     *
     * @param genParameters           生成参数
     * @param createPicParams         创建参数
     * @param user                    用户
     * @param modelRightsVerifyResult 模型权限验证结果
     * @return 生成结果
     */
    public Map<String, Object> generateVideo(GenGenericPara genParameters, CreatePicParams createPicParams,
                                             User user, ModelRightsVerifyResult modelRightsVerifyResult) {
        try {
            String markId = createPicParams.getMarkId();
            Boolean fastHour = createPicParams.getFastHour();
            String platform = createPicParams.getPlatform();
            int relaxWaitTime = createPicParams.getRelaxWaitTime();

            // 获取功能类型
            String feature = getFeatureType(genParameters.getVideoGenerationPara().getVideo_type());

            // 构建视频生成请求参数并保存到PromptRecord
            VideoGenerationParams videoParams = buildVideoGenerationRequest(genParameters, markId);
            savePromptRecord(genParameters, videoParams, user, markId, fastHour, platform, modelRightsVerifyResult, feature);

            // 记录用户生图状态到redis
            redisService.stringSet(markId, user.getLoginName(), 2, TimeUnit.HOURS);

            // 推送任务到队列
            String pushQueueResult = "";
            try {
                if ((relaxWaitTime > 0) && !fastHour) {
                    // 推送到relax等待队列
                    pushQueueResult = loadBalanceService.pushToRelaxWaitQueue(genParameters.getModel_id(), feature, markId, relaxWaitTime);
                } else {
                    // 推送到任务队列
                    pushQueueResult = loadBalanceService.pushToTaskQueue(genParameters.getModel_id(), feature, fastHour, markId, user);
                }
            } catch (Exception e) {
                log.error("视频生成任务推送队列失败", e);
                // 任务状态改为失败
                genService.updatePromptRecordStatus(markId, user.getLoginName());
            }

            // 判断推送队列是否有误
            genService.judgePushQueueError(pushQueueResult, markId, user.getLoginName());

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("markId", markId);
            result.put("index", -1);
            result.put("fastHour", fastHour);
            result.put("featureName", feature);

            return result;

        } catch (Exception e) {
            log.error("视频生成失败，markId: {}, error: {}", createPicParams.getMarkId(), e.getMessage(), e);
            throw new RuntimeException("视频生成失败: " + e.getMessage());
        }
    }

    /**
     * 构建视频生成请求参数
     */
    private VideoGenerationParams buildVideoGenerationRequest(GenGenericPara genParameters, String markId) {
        GenGenericPara.VideoGenerationPara videoPara = genParameters.getVideoGenerationPara();
        Resolution resolution = genParameters.getResolution();
        VideoGenerationParams requestParams = new VideoGenerationParams();
        requestParams.setPrompt(genParameters.getPrompt());
        requestParams.setNegative_prompt(genParameters.getNegative_prompt());
        requestParams.setModel_id(genParameters.getModel_id());
        requestParams.setVideo_type(videoPara.getVideo_type());
        requestParams.setSeed(genParameters.getSeed() != null ? genParameters.getSeed() : -1);
        requestParams.setCallback_url(callback_url);
        requestParams.setMark_id(markId);

        // 图生视频需要img_url，文生视频需要width和height
        if ("image_to_video".equals(videoPara.getVideo_type())) {
            requestParams.setImg_url(videoPara.getImgs().get(0).getImg_url());
        } else if ("text_to_video".equals(videoPara.getVideo_type())) {
            requestParams.setWidth(resolution.getWidth());
            requestParams.setHeight(resolution.getHeight());
        }

        return requestParams;
    }


    /**
     * 保存PromptRecord记录
     */
    private void savePromptRecord(GenGenericPara genParameters, VideoGenerationParams videoParams, User user, String markId,
                                  Boolean fastHour, String platform, ModelRightsVerifyResult modelRightsVerifyResult, String feature) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPrompt(genParameters.getPrompt());
            promptRecord.setNegativePrompt(genParameters.getNegative_prompt());
            promptRecord.setMarkId(markId);
            promptRecord.setModelId(genParameters.getModel_id());
            promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            promptRecord.setPromptParams(JsonUtils.writeToJsonNode(videoParams));
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setDuration(genParameters.getVideoGenerationPara().getDuration());

            // 设置正确的OriginCreate
            String originCreate = "text_to_video".equals(genParameters.getVideoGenerationPara().getVideo_type()) ?
                    OriginCreate.textToVideo.getValue() : OriginCreate.imageToVideo.getValue();
            promptRecord.setOriginCreate(originCreate);
            promptRecord.setFastHour(fastHour != null ? fastHour : Boolean.FALSE);
            promptRecord.setPlatform(platform);
            promptRecord.setCostLumens(modelRightsVerifyResult.getCostLumen());

            // 设置功能类型
            promptRecord.setFeatureName(feature);

            // 设置宽高比
            String aspectRatio = getAspectRatio(genParameters);
            promptRecord.setAspectRatio(aspectRatio);

            // 设置批量生成数为1（视频生成通常是单个）
            promptRecord.setBatchSize(1);

            promptRecordMapper.insert(promptRecord);
            log.info("保存PromptRecord成功，markId: {}", markId);

        } catch (Exception e) {
            log.error("保存PromptRecord失败，markId: {}, error: {}", markId, e.getMessage(), e);
            throw new RuntimeException("保存PromptRecord失败: " + e.getMessage());
        }
    }

    /**
     * 获取功能类型
     */
    private String getFeatureType(String videoType) {
        if ("text_to_video".equals(videoType)) {
            return FeaturesType.textToVideo.getValue();
        } else if ("image_to_video".equals(videoType)) {
            return FeaturesType.imageToVideo.getValue();
        }
        return FeaturesType.textToVideo.getValue(); // 默认值
    }

    /**
     * 获取宽高比
     */
    private String getAspectRatio(GenGenericPara genParameters) {
        GenGenericPara.VideoGenerationPara videoPara = genParameters.getVideoGenerationPara();
        Resolution resolution = genParameters.getResolution();

        if ("text_to_video".equals(videoPara.getVideo_type())) {
            return AspectRatioUtils.getAspectRatio(resolution.getWidth(), resolution.getHeight());
        }

        // 图生视频或未指定尺寸时使用默认值
        return "832 x 480"; // 默认视频尺寸
    }

//    /**
//     * 更新PromptRecord状态为失败
//     */
//    private void updatePromptRecordStatus(String markId, String loginName) {
//        try {
//            LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper();
//            luw.eq(PromptRecord::getMarkId, markId);
//            luw.eq(PromptRecord::getLoginName, loginName);
//            luw.eq(PromptRecord::getUpdateTime, LocalDateTime.now());
//            luw.set(PromptRecord::getDel, Boolean.TRUE);
//            promptRecordMapper.update(null, luw);
//            log.error("任务失败，markId: {}, loginName: {}", markId, loginName);
//        } catch (Exception e) {
//            log.error("更新PromptRecord状态失败", e);
//        }
//    }

    /**
     * 检查是否为视频生成模型
     */
    public boolean isVideoModel(String modelId) {
        // 检查是否为配置的视频生成模型ID
        return modelId != null && modelId.equals(picflowModelId);
    }
}
