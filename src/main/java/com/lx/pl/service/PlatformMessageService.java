package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.db.mysql.community.entity.UserPlatformMessage;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.UserPlatformMessageRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class PlatformMessageService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private UserPlatformMessageRepository userPlatformMessageRepository;

    @Autowired
    private RedissonClient redissonClient;

    public Boolean readPlatformMessage(String platformMessageId, User user) {
        RLock lock = redissonClient.getLock("read:platformMessage:" + user.getId());
        try {
            //修改所有满足条件的
            if (StringUtil.isBlank(platformMessageId)) {
                mongoTemplate.updateMulti(
                        new Query(Criteria.where("accountInfo.userId").is(user.getId())
                                .and("read").is(false)),  // read 为 false
                        new Update().set("read", true).set("readTime", LocalDateTime.now()),
                        UserPlatformMessage.class
                );

            } else {
                //只修改满足条件的一条
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("accountInfo.userId").is(user.getId())
                                .and("id").is(platformMessageId)),
                        new Update().set("read", true).set("readTime", LocalDateTime.now()),
                        UserPlatformMessage.class
                );
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对平台信息：{} 已读报错", user.getId(), platformMessageId, e);
            throw new ServerInternalException("Read PlatformMessage Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public CommPageInfo<UserPlatformMessage> getPlatformMessage(String lastPlatformMessId, Integer pageSize, User user, String platform) {
        List<UserPlatformMessage> platformMessageList = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()));

            // 匹配 platform 为 null 或包含指定 platform（数组类型字段匹配）
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("platform").is(null),
                    Criteria.where("platform").is(platform)  // 对于数组字段，相当于 $in
            ));

            if (StringUtil.isNotBlank(lastPlatformMessId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastPlatformMessId)));
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            platformMessageList = mongoTemplate.find(query, UserPlatformMessage.class);

        } catch (Exception e) {
            log.error("获取个人通知表报错", e);
            throw new ServerInternalException("Error retrieving the Platform  Message list");
        }
        return buildPromptPageInfo(pageSize, platformMessageList);
    }


    // 构建分页结果对象
    private CommPageInfo<UserPlatformMessage> buildPromptPageInfo(Integer pageSize, List<UserPlatformMessage> platformMessageList) {
        CommPageInfo<UserPlatformMessage> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(platformMessageList) ? Collections.emptyList() : platformMessageList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(platformMessageList) ? "" : platformMessageList.get(platformMessageList.size() - 1).getId());
        return commPageInfo;
    }

    public void addPlatformMessage(UserPlatformMessage userPlatformMessage) {
        userPlatformMessage.setCreateTime(LocalDateTime.now());
        userPlatformMessage.setRead(Boolean.FALSE);
        userPlatformMessageRepository.insert(userPlatformMessage);
    }

}
