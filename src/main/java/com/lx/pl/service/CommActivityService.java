package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.activity.CommActivity;
import com.lx.pl.db.mysql.community.entity.CommBlacklist;
import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.db.mysql.community.entity.CommUserActivityRecord;
import com.lx.pl.db.mysql.gen.entity.Album;
import com.lx.pl.db.mysql.gen.entity.PublicFileReview;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.CommActivityMapper;
import com.lx.pl.db.mysql.gen.mapper.CommActivityPrizeSettingsMapper;
import com.lx.pl.db.mysql.gen.mapper.PublicFileReviewMapper;
import com.lx.pl.dto.AlbumResult;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.community.activity.CommActivityDetailResult;
import com.lx.pl.dto.community.activity.CommActivityPostingResult;
import com.lx.pl.dto.community.activity.CommActivityResult;
import com.lx.pl.enums.ClientType;
import com.lx.pl.exception.ServerInternalException;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommActivityService extends ServiceImpl<CommActivityMapper, CommActivity> {

    @Autowired
    private CommActivityMapper commActivityMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PublicFileReviewMapper publicFileReviewMapper;

    public PromptPageInfo<CommActivityResult> getCommActivityList(Integer pageNum, Integer pageSize, User user, String platform) {
        //分页查询任务
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<CommActivity> qw = new LambdaQueryWrapper<>();
        qw.orderByDesc(CommActivity::getCreateTime);
        qw.le(CommActivity::getBeginTime, now);
        qw.eq(CommActivity::getPublish, true);
        qw.like(CommActivity::getPlatform, platform);
        qw.select(CommActivity::getId, CommActivity::getTitle, CommActivity::getBeginTime, CommActivity::getEndTime, CommActivity::getPostEndTime,
                CommActivity::getTags, CommActivity::getCover, CommActivity::getMaxSubmissions,CommActivity::getType,
                CommActivity::getPublish, CommActivity::getReward, CommActivity::getImageNum);

        IPage<CommActivity> page = commActivityMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<CommActivity> commActivityList = page.getRecords();

        List<CommActivityResult> activityResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(commActivityList)) {
            for (CommActivity activity : commActivityList) {
                CommActivityResult activityResult = new CommActivityResult();
                BeanUtils.copyProperties(activity, activityResult);
                activityResultList.add(activityResult);
            }
        }

        PromptPageInfo<CommActivityResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(activityResultList);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        updateCommActivityRecord(user, platform);
        return promptPageInfo;
    }

    public List<CommActivityPostingResult> getCommActivityPostingList(User user,String  platform) {
        LocalDateTime now = LocalDateTime.now();

        // 查询当前开放投稿的活动
        LambdaQueryWrapper<CommActivity> qw = new LambdaQueryWrapper<>();
        qw.eq(CommActivity::getPublish, true)
                .le(CommActivity::getBeginTime, now)
                .gt(CommActivity::getPostEndTime, now)
                .like(CommActivity::getPlatform, platform)
                .select(CommActivity::getId, CommActivity::getTitle, CommActivity::getMaxSubmissions, CommActivity::getType);

        List<CommActivity> commActivities = commActivityMapper.selectList(qw);
        if (CollectionUtils.isEmpty(commActivities)) {
            return Collections.emptyList();
        }

        // 活动ID列表
        List<Long> activityIds = commActivities.stream()
                .map(CommActivity::getId)
                .collect(Collectors.toList());

        // 查询Mongo中用户通过审核的投稿
        Query mongoQuery = new Query();
        mongoQuery.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()));
        mongoQuery.addCriteria(Criteria.where("activityId").in(activityIds));
        List<CommFile> commFiles = mongoTemplate.find(mongoQuery, CommFile.class);

        // Map: 活动ID -> 投稿数
        Map<Long, Long> commFileCountMap = commFiles.stream()
                .collect(Collectors.groupingBy(CommFile::getActivityId, Collectors.counting()));

        // 查询审核中的投稿
        LambdaQueryWrapper<PublicFileReview> pfw = new LambdaQueryWrapper<>();
        pfw.eq(PublicFileReview::getLoginName, user.getLoginName())
                .in(PublicFileReview::getActivityId, activityIds)
                .eq(PublicFileReview::getDel, Boolean.FALSE);
        List<PublicFileReview> publicFileReviewList = publicFileReviewMapper.selectList(pfw);

        Map<String, Long> reviewCountMap = publicFileReviewList.stream()
                .collect(Collectors.groupingBy(PublicFileReview::getActivityId, Collectors.counting()));

        // 组装结果，只保留投稿未满的活动
        List<CommActivityPostingResult> activityPostingResultList = commActivities.stream()
                .filter(activity -> {
                    Long submitted = commFileCountMap.getOrDefault(activity.getId(), 0L);
                    Long reviewing = reviewCountMap.getOrDefault(activity.getId().toString(), 0L);
                    return (submitted + reviewing) < activity.getMaxSubmissions();
                })
                .map(activity -> {
                    CommActivityPostingResult result = new CommActivityPostingResult();
                    BeanUtils.copyProperties(activity, result);
                    return result;
                })
                .collect(Collectors.toList());

        return activityPostingResultList;
    }


    public CommActivityDetailResult getCommActivityDetail(Long id, User user,String platform) {
        CommActivity activity = commActivityMapper.selectById(id);

        CommActivityDetailResult activityResult = new CommActivityDetailResult();
        BeanUtils.copyProperties(activity, activityResult);

        Boolean notExitKeyAndValueInSet = redisService.isNotExitKeyAndValueInSet(LogicConstants.COMM_ACTIVITY_PUBLIC, id);

        Boolean notExitKeyAndValueInSetByPlatform = redisService.isNotExitKeyAndValueInSet(LogicConstants.COMM_ACTIVITY_PUBLIC + ":" + platform.toLowerCase(),id);

        // 此活动在缓存当中 处理用户已读情况
        if (notExitKeyAndValueInSet || notExitKeyAndValueInSetByPlatform) {
            addActivityIdToUser(user, id,platform);
        }
        return activityResult;
    }

    /**
     * 向指定用户的 activityIds 集合中添加一个 activityId（不会重复添加）
     *
     * @param user       登录用户
     * @param activityId 活动ID
     */
    public void addActivityIdToUser(User user, Long activityId, String platform) {

        if (hasUnreadPublicActivities(user, platform)) {

            CommActivity commActivity = commActivityMapper.selectById(activityId);

            // 检查活动是否为全平台
            boolean isAllPlatformActivity = ClientType.isAllPlatformSelected(commActivity.getPlatform());


            // 如果是全平台活动，则更新公共数据
            if (isAllPlatformActivity) {
                Query query = new Query(Criteria.where("userId").is(user.getId()));
                Update update = new Update()
                        .addToSet("activityIds", activityId)
                        .setOnInsert("userId", user.getId());
                UpdateResult result = mongoTemplate.upsert(query, update, CommUserActivityRecord.class);

                // 更新成功后，增加Redis中公共活动的已读计数
                if (result.getUpsertedId() != null || result.getModifiedCount() > 0) {
                    redisService.incrementFieldInHash(
                            LogicConstants.COMM_ACTIVITY_USER_NUM,
                            user.getLoginName(),
                            1
                    );
                }
            } else {
                // 获取活动本身支持的平台
                Set<String> activityPlatforms = Arrays.stream(commActivity.getPlatform().split(","))
                        .map(String::trim)
                        .map(String::toLowerCase)
                        .collect(Collectors.toSet());
                // 如果是特定平台的活动，则只更新当前平台的数据
                Update update = new Update().setOnInsert("userId", user.getId());
                for(String one : activityPlatforms){
                    switch (one.toLowerCase()) {
                        case "web":
                            update.addToSet("activityWebIds", activityId);
                            break;
                        case "ios":
                            update.addToSet("activityIosIds", activityId);
                            break;
                        case "android":
                            update.addToSet("activityAndroidIds", activityId);
                            break;
                    }
                }

                Query query = new Query(Criteria.where("userId").is(user.getId()));
                UpdateResult result = mongoTemplate.upsert(query, update, CommUserActivityRecord.class);

                // 更新成功后，只增加Redis中当前平台的已读计数
                if (result.getUpsertedId() != null || result.getModifiedCount() > 0) {
                    for(String one : activityPlatforms){
                        redisService.incrementFieldInHash(
                                LogicConstants.COMM_ACTIVITY_USER_NUM + ":" + one.toLowerCase(),
                                user.getLoginName(),
                                1
                        );
                    }

                }
            }

        }
    }

    private boolean hasUnreadPublicActivities(User user, String platform) {
        int totalCount = 0;

        // 获取社区活动公共数量
        Set<Long> commActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(commActivityIds)) {
            totalCount += commActivityIds.size();
        }

        // 获取各个平台的社区活动数量
        if (platform != null && !platform.isEmpty()) {
            Set<Long> commActivityIdsByPlatform = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC + ":" + platform.toLowerCase());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(commActivityIdsByPlatform)) {
                totalCount += commActivityIdsByPlatform.size();
            }
        }

        if (totalCount <= 0) {
            return false;
        }

        String loginName = user.getLoginName();
        // 获取用户已查看活动数量
        int userCommActivityNum = (int) Optional.ofNullable(redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM, loginName)).orElse(0);

        // 获取用户各个平台已查看活动数量
        int userCommActivityNumByPlatform = (int) Optional.ofNullable(redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM + ":" + platform.toLowerCase(), loginName)).orElse(0);

        int totalUserViewedCount = userCommActivityNum + userCommActivityNumByPlatform;

        return totalCount > totalUserViewedCount;
    }


    public void updateCommActivityRecord(User user, String platform) {

        // 如果活动全部已读 则不做任何操作
        if (!hasUnreadPublicActivities(user, platform)){
            return;
        }
        // 初始化MongoDB的更新对象，并设置一个标志位来判断是否需要执行数据库更新
        Update update = new Update().setOnInsert("userId", user.getId());
        boolean needsMongoUpdate = false;

        // 1. 处理全平台通用的活动
        // 从Redis获取所有公共活动的ID
        Set<Long> publicActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC);
        // 从Redis获取用户已读的公共活动数量
        int userActivityCount = (int) Optional.ofNullable(
                redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM, user.getLoginName())
        ).orElse(0);

        // 如果有新的未读公共活动，则准备更新
        boolean publicNeedsUpdate = org.apache.commons.collections4.CollectionUtils.isNotEmpty(publicActivityIds) && publicActivityIds.size() > userActivityCount;
        if (publicNeedsUpdate) {
            update.addToSet("activityIds").each(publicActivityIds.toArray()); // 将新活动ID添加到更新集中
            needsMongoUpdate = true; // 标记需要更新数据库
        }

        // 2. 处理特定平台的活动
        Map<String, Set<Long>> platformUpdates = new HashMap<>();
        if (platform != null && !platform.isEmpty()) {
            String currentPlatform = platform.toLowerCase();
            Set<Long> platformActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC + ":" + currentPlatform);
            int userPlatformCount = (int) Optional.ofNullable(redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM + ":" + currentPlatform, user.getLoginName())).orElse(0);

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(platformActivityIds) && platformActivityIds.size() > userPlatformCount) {
                List<CommActivity> activities = commActivityMapper.selectBatchIds(platformActivityIds);
                for (CommActivity activity : activities) {
                    Set<String> activityPlatforms = Arrays.stream(activity.getPlatform().split(","))
                            .map(String::trim).map(String::toLowerCase).collect(Collectors.toSet());
                    for (String p : activityPlatforms) {
                        platformUpdates.computeIfAbsent(p, k -> new HashSet<>()).add(activity.getId());
                    }
                }
            }
        }

        if (!platformUpdates.isEmpty()) {
            needsMongoUpdate = true;
            for (Map.Entry<String, Set<Long>> entry : platformUpdates.entrySet()) {
                String p = entry.getKey();
                Object[] ids = entry.getValue().toArray();

                switch (p) {
                    case "web":
                        update.addToSet("activityWebIds").each(ids);
                        break;
                    case "ios":
                        update.addToSet("activityIosIds").each(ids);
                        break;
                    case "android":
                        update.addToSet("activityAndroidIds").each(ids);
                        break;
                    default:
                        // 其他平台如果需要处理，可以在这里加
                        break;
                }
            }
        }

        // 3. 如果有任何需要更新的内容，则执行数据库和缓存操作
        if (needsMongoUpdate) {
            // 执行批量更新或插入操作
            Query query = new Query(Criteria.where("userId").is(user.getId()));
            mongoTemplate.upsert(query, update, CommUserActivityRecord.class);

            // 更新Redis中的已读计数
            // 更新公共活动的已读数量
            if (publicNeedsUpdate) {
                redisService.putDataToHash(
                        LogicConstants.COMM_ACTIVITY_USER_NUM,
                        user.getLoginName(),
                        publicActivityIds.size()
                );
            }
            // 更新各个平台的已读活动数量
            for (String p : platformUpdates.keySet()) {
                Set<Long> totalIdsForPlatform = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC + ":" + p);
                redisService.putDataToHash(
                        LogicConstants.COMM_ACTIVITY_USER_NUM + ":" + p,
                        user.getLoginName(),
                        totalIdsForPlatform.size()
                );
            }
        }
    }


}
