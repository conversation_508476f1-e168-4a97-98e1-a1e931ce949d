package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.db.mysql.gen.entity.QuestionnaireAnswer;
import com.lx.pl.db.mysql.gen.entity.QuestionnaireRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.QuestionnaireAnswerMapper;
import com.lx.pl.db.mysql.gen.mapper.QuestionnaireRecordMapper;
import com.lx.pl.dto.QuestionnaireAnswerParams;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

@Service
@Slf4j
public class QuestionnaireAnswerService {

    public static final String WEB_QUESTIONNAIRE_ANSWER = "web_questionnaire_answer"; // web问卷用户信息

    public static final String IOS_QUESTIONNAIRE_ANSWER = "ios_questionnaire_answer"; // ios问卷用户信息

    public static final String ANDROID_QUESTIONNAIRE_ANSWER = "android_questionnaire_answer"; // android问卷用户信息

    public static final String WEB_QUESTIONNAIRE_RECORD = "web_questionnaire_record"; // web问卷

    public static final String IOS_QUESTIONNAIRE_RECORD = "ios_questionnaire_record"; // ios问卷

    public static final String ANDROID_QUESTIONNAIRE_RECORD = "android_questionnaire_record"; // android问卷

    @Autowired
    private QuestionnaireAnswerMapper questionnaireAnswerMapper;

    @Autowired
    private QuestionnaireRecordMapper questionnaireRecordMapper;

    @Autowired
    private PayLumenRecordService payLumenRecordService;

    @Autowired
    private RedisService redisService;


    public Long getQuestionnaireAnswer(String questionnaireId, String platform, User user) {
        LambdaQueryWrapper<QuestionnaireAnswer> lqw = new LambdaQueryWrapper();
        lqw.eq(QuestionnaireAnswer::getQuestionnaireId, questionnaireId);
        lqw.eq(QuestionnaireAnswer::getLoginName, user.getLoginName());
        lqw.eq(QuestionnaireAnswer::getPlatform, platform);
        lqw.eq(QuestionnaireAnswer::getDel, Boolean.FALSE);
        return questionnaireAnswerMapper.selectCount(lqw);
    }


    public Boolean executeAnswer(QuestionnaireAnswerParams params, String platform, User user) {

        try {
            String loginName = user.getLoginName();

            //将用户的问卷答案入库
            QuestionnaireAnswer questionnaireAnswer = new QuestionnaireAnswer();
            BeanUtils.copyProperties(params, questionnaireAnswer);
            questionnaireAnswer.setLoginName(loginName);
            questionnaireAnswer.setPlatform(platform);
            questionnaireAnswer.setCreateBy(user.getLoginName());
            questionnaireAnswer.setCreateTime(LocalDateTime.now());

            int result = questionnaireAnswerMapper.insert(questionnaireAnswer);

            //提交问卷成功，则赠送lumen信息
            if (result > 0) {
                switch (platform) {
                    case "web":
                        redisService.addOBjectDataToSet(WEB_QUESTIONNAIRE_ANSWER, loginName);
                        //赠送lumen币
                        payLumenRecordService.giftLumen(user, 5);
                        break;
                    case "ios":
                        redisService.addOBjectDataToSet(IOS_QUESTIONNAIRE_ANSWER, loginName);
                        //赠送lumen币
                        payLumenRecordService.giftLumen(user, 5);
                        break;
                    case "android":
                        redisService.addOBjectDataToSet(ANDROID_QUESTIONNAIRE_ANSWER, loginName);
                        //赠送lumen币
                        payLumenRecordService.giftLumen(user, 5);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("问卷答题失败，失败原因：", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    public QuestionnaireRecord getQuestionnaireDetail(String questionnaireId, String platform, User user) throws JsonProcessingException {
        QuestionnaireRecord questionnaireRecord = new QuestionnaireRecord();

        String record = "";
        switch (platform) {
            case "web":
                record = (String) redisService.get(WEB_QUESTIONNAIRE_RECORD);
                break;
            case "ios":
                record = (String) redisService.get(IOS_QUESTIONNAIRE_RECORD);
                break;
            case "android":
                record = (String) redisService.get(ANDROID_QUESTIONNAIRE_RECORD);
                break;
        }
        if (StringUtils.isNotBlank(record)) {
            questionnaireRecord = JsonUtils.fromString(record, QuestionnaireRecord.class);
        } else {
            LambdaQueryWrapper<QuestionnaireRecord> lqw = new LambdaQueryWrapper();
            if (StringUtil.isNotBlank(questionnaireId)) {
                lqw.eq(QuestionnaireRecord::getId, questionnaireId);
            }
            lqw.eq(QuestionnaireRecord::getPlatform, platform);
            lqw.eq(QuestionnaireRecord::getPublish, 5);
            lqw.eq(QuestionnaireRecord::getDel, Boolean.FALSE);
            questionnaireRecord = questionnaireRecordMapper.selectOne(lqw);

            //放入对应的缓存中
            if (!Objects.isNull(questionnaireRecord)) {
                switch (platform) {
                    case "web":
                        redisService.set(WEB_QUESTIONNAIRE_RECORD, JsonUtils.writeToString(questionnaireRecord));
                        break;
                    case "ios":
                        redisService.set(IOS_QUESTIONNAIRE_RECORD, JsonUtils.writeToString(questionnaireRecord));
                        break;
                    case "android":
                        redisService.set(ANDROID_QUESTIONNAIRE_RECORD, JsonUtils.writeToString(questionnaireRecord));
                        break;
                }
            }
        }

        LocalDateTime nowTime = LocalDateTime.now();
        //判断是否在问卷时间范围内,否则将内容置为空
        if (!Objects.isNull(questionnaireRecord) && !(questionnaireRecord.getEndTime().isAfter(nowTime))) {
            questionnaireRecord.setDetails("");

            //删除对应的数据
            switch (platform) {
                case "web":
                    redisService.unlink(WEB_QUESTIONNAIRE_ANSWER);
                    break;
                case "ios":
                    redisService.unlink(IOS_QUESTIONNAIRE_ANSWER);
                    break;
                case "android":
                    redisService.unlink(ANDROID_QUESTIONNAIRE_ANSWER);
                    break;
            }
        }

        return questionnaireRecord;
    }
}
