package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RedisService<T> {

    @Resource
    RedisTemplate<String, Object> redisTemplate;
//    @Resource
//    RedisTemplate<String, Integer> redisTemplate2;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedissonClient redissonClient;

    /**
     * -- Lua 脚本：判断 TTL 状态并处理
     * local ttl = redis.call('TTL', KEYS[1])
     * if ttl == -1 then
     * redis.call('DEL', KEYS[1])  -- 删除键
     * return -1  -- 表示删除操作
     * elseif ttl > 0 then
     * redis.call('HSET', KEYS[1], ARGV[1], ARGV[2])  -- 设置哈希字段
     * return 1  -- 表示设置成功
     * else
     * return 0  -- 键不存在或 TTL 为 -2
     * end
     */
    public static final String LUA_SCRIPT_SET_HASH_VALUE = "local ttl = redis.call('TTL', KEYS[1]) "
            + "if ttl == -1 then " + "redis.call('DEL', KEYS[1]) "
            + "return -1 " + "elseif ttl > 0 then "
            + "redis.call('HSET', KEYS[1], ARGV[1], ARGV[2]) " + "return 1 " + "else " + "return 0 end";

    public static final String LUA_SCRIPT_HELEN = "local ttl = redis.call('TTL', KEYS[1]) "
            + "if ttl == -1 then " + "redis.call('DEL', KEYS[1]) "
            + "return 0 " + " elseif ttl > 0 then "
            + "local result = redis.call('HLEN', KEYS[1]) " +
            "  return result " + "else " + "return 0 end";

    public static final String LUA_SCRIPT_COMFY_SIZE_INCR =
            "local current = tonumber(redis.call('GET', KEYS[1]) or '0')\n" +
                    "if current >= tonumber(ARGV[1]) then\n" +
                    "    return false\n" +
                    "else\n" +
                    "    redis.call('INCR', KEYS[1])\n " +
                    "    return true\n" +
                    "end";

    public static final String LUA_SCRIPT_COMFY_SIZE_INCR1 =
            "local current = tonumber(redis.call('GET', KEYS[1]) or '0')\n" +
                    "if current >= tonumber(ARGV[1]) then\n" +
                    "    return false\n" +
                    "end\n" +
                    "local totalQueueSize = redis.call('LLEN', KEYS[2]) + redis.call('LLEN', KEYS[3])\n" +
                    "if totalQueueSize == 0 then\n" +
                    "    return false\n" +
                    "end\n" +
                    "redis.call('INCR', KEYS[1])\n" +
                    "return true\n";


    public static final String LUA_SCRIPT_COMFY_SIZE_DECR =
            "local current = tonumber(redis.call('GET', KEYS[1]) or '0')\n" +
                    "if current > 0 then\n" +
                    "    redis.call('DECR', KEYS[1])\n" +
                    "    return current - 1\n" +
                    "else\n" +
                    "    return 0\n" +
                    "end";

    // Lua 脚本
    public static final String LUA_SCRIPT_GET_AND_REMOVE_EXPIRED_TASKS =
            "local currentTime = tonumber(ARGV[1])\n" +
                    "local queueName = KEYS[1]\n" +
                    "local expiredTasks = redis.call('ZRANGEBYSCORE', queueName, 0, currentTime)\n" +
                    "if #expiredTasks > 0 then\n" +
                    "    redis.call('ZREM', queueName, unpack(expiredTasks))\n" +
                    "end\n" +
                    "return expiredTasks";

    // 对hash字段值减1，值<=0时删除字段，字段全部删除后删除key
    public static final String LUA_SCRIPT_HASH_DECREMENT_AND_DEL =
            "if redis.call('EXISTS', KEYS[1]) == 0 then\n" +
                    "    return\n" +
                    "end\n" +
                    "if redis.call('HEXISTS', KEYS[1], ARGV[1]) == 0 then\n" +
                    "    return\n" +
                    "end\n" +
                    "local current_value = redis.call('HGET', KEYS[1], ARGV[1])\n" +
                    "local num_value = tonumber(current_value)\n" +
                    "if num_value == nil then\n" +
                    "    return\n" +
                    "end\n" +
                    "local new_value = num_value - 1\n" +
                    "if new_value <= 0 then\n" +
                    "    redis.call('HDEL', KEYS[1], ARGV[1])\n" +
                    "    local field_count = redis.call('HLEN', KEYS[1])\n" +
                    "    if field_count == 0 then\n" +
                    "        redis.call('DEL', KEYS[1])\n" +
                    "    end\n" +
                    "else\n" +
                    "    redis.call('HSET', KEYS[1], ARGV[1], new_value)\n" +
                    "end";

    // 对hash字段值减1
    public static final String LUA_SCRIPT_HASH_DECREMENT =
            "local current = redis.call('HGET', KEYS[1], ARGV[1])\n" +
                    "if current and tonumber(current) and tonumber(current) > 0 then\n" +
                    "    redis.call('HINCRBY', KEYS[1], ARGV[1], -1)\n" +
                    "end";


    public void set(String key, T value) {
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(key, value);
    }

    public void set(String key, T value, long timeOutSec) {
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(key, value, timeOutSec, TimeUnit.SECONDS);
    }

    public void set(String key, T value, long timeOutSec, TimeUnit unit) {
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(key, value, timeOutSec, unit);
    }

    public T get(String key) {
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        return (T) valueOperations.get(key);
    }


    public void stringSet(String key, String value, long timeOutSec, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value, timeOutSec, unit);
    }

    public void stringSet(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    public String stringGet(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    //模糊匹配扫描相关的key
    public List<String> getListKey(String fix) {
        ScanOptions options = ScanOptions.scanOptions().match(fix).build();
        Cursor<byte[]> cursor = stringRedisTemplate.getConnectionFactory().getConnection().scan(options);

        List<String> matchedKeys = new ArrayList<>();
        while (cursor.hasNext()) {
            byte[] keyBytes = cursor.next();
            // 反序列化为字符串
            String key = new String(keyBytes, StandardCharsets.UTF_8);
            matchedKeys.add(key);
        }
        return matchedKeys;
    }

    public void expire(String key) {
        redisTemplate.expire(key, 3600, TimeUnit.SECONDS);
    }

    public void expire(String key, long timeout, TimeUnit timeUnit) {
        redisTemplate.expire(key, timeout, timeUnit);
    }

    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    /**
     * 使用 UNLINK 命令异步删除键
     *
     * @param key 要删除的键
     */
    public void unlink(String key) {
        redisTemplate.unlink(key);
    }

    // 向Hash中添加数据
    public void putDataToHash(String key, String field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }

    //批量想hash中添加数据
    public void putAllToHash(String key, Map<String, Integer> values) {
        redisTemplate.opsForHash().putAll(key, values);
    }

    public void putAllObjcetToHash(String key, Map<String, Object> values) {
        redisTemplate.opsForHash().putAll(key, values);
    }


    public void putDataToHash(String key, String field, Object value, long timeOutSec, TimeUnit unit) {
        redisTemplate.opsForHash().put(key, field, value);
        redisTemplate.expire(key, timeOutSec, unit);
    }

    /**
     * @param key   哈希表的键
     * @param field 哈希表的字段
     * @param value 增加的值
     */
    public void incrementHashValue(String key, String field, int value) {
        // 如果字段不存在，increment 会自动初始化为 0 再加 value
        redisTemplate.opsForHash().increment(key, field, value);
    }

    /**
     * 判断键存在且 TTL 大于 0 后设置哈希字段值
     *
     * @param hashKey 哈希键
     * @param field   哈希字段
     * @param value   要设置的值
     * @return 是否成功设置字段值
     */
    public boolean putHashStrFieldIfExistsAndTTLValid(String hashKey, String field, Object value) {
        // 创建 Redis 脚本对象
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_SET_HASH_VALUE);
        redisScript.setResultType(Long.class);
        Long execute = redisTemplate.execute(redisScript,
                redisTemplate.getStringSerializer(),
                new Jackson2JsonRedisSerializer<>(Long.class),
                Collections.singletonList(hashKey),
                field, String.valueOf(value));
        return execute != null && execute > 0;
    }

    public void incrementCountByHashKey(String key, String hashKey, long value) {
        redisTemplate.opsForHash().increment(key, hashKey, value);
    }

    public boolean hasKey(String key) {
        Boolean b = redisTemplate.hasKey(key);
        return b != null && b;
    }

    public boolean hasHashField(String key, String field) {
        return Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field));
    }


    // 从Hash中获取数据
    public Object getDataFromHash(String key, String field) {
        if (StringUtil.isBlank(key)) {
            return null;
        }
        return redisTemplate.opsForHash().get(key, field);
    }

    /**
     * 增加哈希表中某个字段的值
     *
     * @param key       Redis 哈希表的键
     * @param field     哈希表中的字段
     * @param increment 增加的值（可以是负数）
     * @return 增加后的值
     */
    public Long incrementFieldInHash(String key, String field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    public Long decrementFieldInHash(String key, String field, long decrement) {
        return redisTemplate.opsForHash().increment(key, field, -decrement);
    }

    public Map<Object, Object> getHashmap(String name) {
        return redisTemplate.opsForHash().entries(name);
    }

    //从Hash中获取指定key的所有键
    public List<String> getAllKeysFromHash(String key) {
        // 使用opsForHash().entries()来获取哈希表中的所有字段和值
        Map<Object, Object> hashEntries = redisTemplate.opsForHash().entries(key);

        // 判断map是否为空
        if (hashEntries == null || hashEntries.isEmpty()) {
            // 如果map为空，则返回空的List
            return Collections.emptyList();
        } else {
            // 如果map不为空，则遍历map中的所有key，并将它们收集到List中
            return hashEntries.keySet().stream().map(Object::toString) // 确保键是String类型
                    .collect(Collectors.toList());
        }
    }

    public long countHashKeyAndTTLValid(String loginName) {
        // 创建 Redis 脚本对象
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_HELEN);
        redisScript.setResultType(Long.class);
        Long execute = redisTemplate.execute(redisScript,
                redisTemplate.getStringSerializer(),
                new Jackson2JsonRedisSerializer<>(Long.class),
                Collections.singletonList(loginName),
                "1");
        return execute == null ? 0 : execute;
    }

    /**
     * 获取 Redis 中指定 key 的 Hash 结构数据，并以 Map 形式返回
     *
     * @param key Redis 的 Hash Key
     * @return Map<String, Object> 类型的 Hash 数据
     */
    public Map<String, Object> getHashAsMap(String key) {
        HashOperations<String, String, Object> hashOperations = redisTemplate.opsForHash();
        return hashOperations.entries(key);
    }


    // 删除Hash中的一个字段
    public Long deleteFieldFromHash(String key, String field) {
        return redisTemplate.opsForHash().delete(key, field);
    }

    //zset获取元素
    public Object getDataFromZset(String key, String value) {
        return redisTemplate.opsForZSet().score(key, value);
    }

    //zset添加元素
    public void addDataToZset(String key, String value, Double newScore) {
        redisTemplate.opsForZSet().add(key, value, newScore);
    }

    public Long addDataToSet(String key, Long value) {
        return redisTemplate.opsForSet().add(key, value);
    }

    public void addDataToSet(String key, Set values) {
        redisTemplate.opsForSet().add(key, values.toArray());
    }

    public void addOBjectDataToSet(String key, Object value) {
        redisTemplate.opsForSet().add(key, value);
    }

    public boolean isValueInSet(String key, Object value) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value));
    }

    public boolean isKeyExists(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /*
      key 不存在，或key 存在，则返回为true
     */
    public boolean isNotExitKeyAndValueInSet(String key, Object value) {
        if (!isKeyExists(key)) {
            return Boolean.TRUE;
        }
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value));
    }

    public void delDataToSet(String key, Long value) {
        redisTemplate.opsForSet().remove(key, value);
    }

    public Set<Object> getRandomFormSet(String key, Integer num) {
        return redisTemplate.opsForSet().distinctRandomMembers(key, num);
    }

    public Set<Object> getDataFromSet(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 左进 - 向列表左侧添加元素 (LPUSH)
     *
     * @param key   Redis中的key
     * @param value 要添加的值
     */
    public void leftPush(String key, Object value) {
        redisTemplate.opsForList().leftPush(key, value);
    }

    /**
     * 将 List<String> 类型的数据推送到 Redis 列表左侧，并且每个值加上双引号
     *
     * @param key    Redis 键
     * @param values List<String> 要推送的字符串集合
     * @return 返回列表长度
     */
    public Long leftPushAllJustForWait(String key, List<String> values) {
        // 将 List<String> 中的每个值加上双引号
        List<String> quotedValues = values.stream()
                .map(value -> "\"" + value + "\"")  // 为每个字符串加上双引号
                .collect(Collectors.toList());

        // 将 key 和 quotedValues 转换为字节数组
        byte[] rawKey = key.getBytes();
        byte[][] rawValues = quotedValues.stream()
                .map(value -> value.getBytes())  // 将每个字符串加双引号后的值转换为字节数组
                .toArray(byte[][]::new);

        // 使用 RedisTemplate 执行 LPUSH 操作
        return redisTemplate.execute((RedisCallback<Long>) connection -> {
            return connection.lPush(rawKey, rawValues);  // 执行 lPush 操作
        });
    }

    /**
     * 左进数组 - 向列表左侧添加元素 (LPUSH)
     *
     * @param key  Redis中的key
     * @param list 要添加的值
     */
    public void leftPushAll(String key, List<String> list) {
        redisTemplate.opsForList().leftPushAll(key, list);
    }

    public void leftPush(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForList().leftPush(key, value);
        redisTemplate.expire(key, timeout, unit);
    }


    /**
     * 右出 - 从列表右侧弹出元素 (RPOP)
     *
     * @param key Redis中的key
     * @return 弹出的值
     */
    public Object rightPop(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    public Object pollTaskBlob(String queueName, long timeout, TimeUnit unit) {
        return redisTemplate.opsForList().rightPop(queueName, timeout, unit);
    }

    /**
     * 获取列表长度
     *
     * @param key Redis中的key
     * @return 列表长度
     */
    public Long listSize(String key) {
        return redisTemplate.opsForList().size(key);
    }

    /**
     * 获取指定索引位置的元素
     *
     * @param key   Redis中的key
     * @param index 索引位置
     * @return 指定位置的值
     */
    public Object getElementAtIndex(String key, long index) {
        return redisTemplate.opsForList().index(key, index);
    }

    /**
     * 移除list中（从头到尾（从左到右）第一个元素）
     *
     * @param key
     * @param value
     * @return
     */
    public Long listRemoveValue(String key, Object value) {
        return redisTemplate.opsForList().remove(key, 1, value);
    }


    /**
     * 获取list结果集
     *
     * @param key
     * @return
     */
    public List<Object> getList(String key) {
        // 获取整个列表数据
        List<Object> list = redisTemplate.opsForList().range(key, 0, -1);
        return list;
    }

    public Long getQueueSize(String queueName) {
        return redisTemplate.opsForList().size(queueName);
    }

    public long incrementTaskCounter(String queueName) {
        return stringRedisTemplate.opsForValue().increment(queueName);
    }

    public boolean comfySizeCheck(String key, int limit, String fairQueueKey, String unfairQueueKey) {
        String scriptSha1 = calcSha1(LUA_SCRIPT_COMFY_SIZE_INCR1);

        // 检查脚本是否已经缓存
        List<Boolean> exists = redisTemplate.execute(
                (redisConnection) -> redisConnection.scriptExists(scriptSha1),
                true);

        // 创建 Redis 脚本对象
        DefaultRedisScript<Boolean> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_COMFY_SIZE_INCR1);
        redisScript.setResultType(Boolean.class);


        // 检查 Redis 中是否已缓存此脚本
        AtomicBoolean success = new AtomicBoolean(false);
        if (exists != null && exists.get(0)) {
            // 如果脚本已缓存，使用 EVALSHA 执行
            this.execute(connection -> {
                Object o = connection.evalSha(scriptSha1.getBytes(StandardCharsets.UTF_8), ReturnType.BOOLEAN, 3, this.serializeKey(key),
                        this.serializeKey(fairQueueKey),
                        this.serializeKey(unfairQueueKey),
                        this.serializeKey(Integer.toString(limit)));
                if (o instanceof Boolean) {
                    success.set((Boolean) o);
                }
                return o;
            });
        } else {
            this.execute(connection -> {
                Object o = connection.eval(LUA_SCRIPT_COMFY_SIZE_INCR1.getBytes(StandardCharsets.UTF_8), ReturnType.BOOLEAN, 3, this.serializeKey(key),
                        this.serializeKey(fairQueueKey),
                        this.serializeKey(unfairQueueKey),
                        this.serializeKey(Integer.toString(limit)));
                if (o instanceof Boolean) {
                    success.set((Boolean) o);
                }
                return o;
            });
        }
        // 返回结果
        return success.get();
    }


    public void comfySizeDecr(String key) {
        String scriptSha1 = calcSha1(LUA_SCRIPT_COMFY_SIZE_DECR);
        // 检查脚本是否已经缓存
        List<Boolean> exists = redisTemplate.execute(
                (redisConnection) -> redisConnection.scriptExists(scriptSha1),
                true);

        // 创建 Redis 脚本对象
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_COMFY_SIZE_DECR);
        redisScript.setResultType(Long.class);


        // 检查 Redis 中是否已缓存此脚本
        if (exists != null && exists.get(0)) {
            // 如果脚本已缓存，使用 EVALSHA 执行
            this.execute(connection -> {
                Object o = connection.evalSha(scriptSha1.getBytes(StandardCharsets.UTF_8), ReturnType.BOOLEAN, 1, this.serializeKey(key));
                return o;
            });
        } else {
            this.execute(connection -> {
                Object o = connection.eval(LUA_SCRIPT_COMFY_SIZE_DECR.getBytes(StandardCharsets.UTF_8), ReturnType.BOOLEAN, 1, this.serializeKey(key));
                return o;
            });
        }
    }

    // 计算 SHA-1 值
    public String calcSha1(String strin) {
        try {
            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            byte[] hashBytes = sha1.digest(strin.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                hexString.append(String.format("%02x", b));
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | java.io.UnsupportedEncodingException e) {
            throw new RuntimeException("SHA-1 calculation failed", e);
        }
    }

    public void decrementTaskCounter(String queueName) {
        stringRedisTemplate.opsForValue().decrement(queueName);
    }

    public void resetTaskCounter(String queueName) {
        stringRedisTemplate.opsForValue().set(queueName, "0");
    }

    /**
     * 尝试获取锁
     *
     * @param lockKey    锁的键
     * @param value      锁的值，可以使用当前线程或随机字符串
     * @param expireTime 过期时间
     * @param timeUnit   时间单位
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, String value, long expireTime, TimeUnit timeUnit) {
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, value, expireTime, timeUnit);
        return Boolean.TRUE.equals(success);
    }

    /**
     * 释放锁
     *
     * @param lockKey 锁的键
     * @param value   锁的值，确保只释放自己持有的锁
     */
    public void unlock(String lockKey, String value) {
        Object currentValue = redisTemplate.opsForValue().get(lockKey);
        if (value.equals(currentValue)) {
            redisTemplate.delete(lockKey);
        }
    }


    // 执行Redis管道操作的通用方法
    public void executePipelined(RedisCallback<Object> callback) {
        redisTemplate.executePipelined(callback);
    }

    public void execute(RedisCallback<Object> callback) {
        redisTemplate.execute(callback);
    }

    // 序列化Redis键
    public byte[] serializeKey(String key) {
        return redisTemplate.getStringSerializer().serialize(key);
    }

    // 序列化哈希表中的字段
    public byte[] serializeHashKey(String hashKey) {
        return redisTemplate.getStringSerializer().serialize(hashKey);
    }

    // 序列化哈希表中的值
    public byte[] serializeHashValue(Object value) throws JsonProcessingException {
        // 假设用Jackson序列化对象，确保导入了Jackson依赖
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsBytes(value);
    }

    public void addTaskToDelayQueue(String queueName, String markId, long delayInSeconds) {
        long timestamp = System.currentTimeMillis() / 1000 + delayInSeconds;
        stringRedisTemplate.opsForZSet().add(queueName, markId, timestamp);
    }

//    public Set<String> popExpiredTasksFromDelayQueue(String queueName) {
//        long currentTime = System.currentTimeMillis() / 1000;
//        Set<String> expiredTasks = stringRedisTemplate.opsForZSet().rangeByScore(queueName, 0, currentTime); // 获取到期的任务
//        if (!expiredTasks.isEmpty()) {
//            stringRedisTemplate.opsForZSet().remove(queueName, expiredTasks.toArray(new String[0])); // 移除到期的任务
//        }
//        return expiredTasks;
//    }

    public List<String> popExpiredTasksFromDelayQueue(String queueName) {
        long currentTime = System.currentTimeMillis() / 1000;

        // 计算脚本的 SHA1 值
        String scriptSha1 = calcSha1(LUA_SCRIPT_GET_AND_REMOVE_EXPIRED_TASKS);

        // 检查脚本是否已经缓存
        List<Boolean> exists = stringRedisTemplate.execute(
                (redisConnection) -> redisConnection.scriptExists(scriptSha1),
                true);

        // 创建 Redis 脚本对象
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_GET_AND_REMOVE_EXPIRED_TASKS);
        redisScript.setResultType(List.class);

        List<byte[]> expiredTasksList = null;

        // 检查 Redis 中是否已缓存此脚本
        if (exists != null && exists.get(0)) {
            // 脚本已经缓存，使用 EVALSHA 执行
            expiredTasksList = stringRedisTemplate.execute(
                    (redisConnection) -> redisConnection.evalSha(scriptSha1, ReturnType.MULTI, 1, queueName.getBytes(),
                            String.valueOf(currentTime).getBytes()), true);
        } else {
            expiredTasksList = stringRedisTemplate.execute(
                    (redisConnection) -> redisConnection.eval(LUA_SCRIPT_GET_AND_REMOVE_EXPIRED_TASKS.getBytes(StandardCharsets.UTF_8),
                            ReturnType.MULTI, 1, queueName.getBytes(), String.valueOf(currentTime).getBytes()),
                    true);
        }

        // 将 byte[] 转换为 String，并返回一个 List<String>
        List<String> result = new ArrayList<>();
        if (expiredTasksList != null) {
            for (byte[] task : expiredTasksList) {
                result.add(new String(task, StandardCharsets.UTF_8));
            }
        }

        return result;
    }

    /**
     * 获取zset的数据量
     *
     * @param queueName
     * @return
     */
    public int getZsetSize(String queueName) {
        Long size = redisTemplate.opsForZSet().size(queueName);
        return size != null ? size.intValue() : 0;
    }


    /**
     * 删除zset中指定的数据
     *
     * @param queueName
     * @param markId
     * @return
     */
    public void removeTaskFromDelayQueue(String queueName, String markId) {
        stringRedisTemplate.opsForZSet().remove(queueName, markId);
    }

    public Long zcard(String zsetKey) {
        return stringRedisTemplate.opsForZSet().zCard(zsetKey);
    }

    public void zadd(String zsetKey, Set<ZSetOperations.TypedTuple<String>> tuples) {
        stringRedisTemplate.opsForZSet().add(zsetKey, tuples);
    }

    public void zadd(String zsetKey, ScoreSortedQueue sortedQueue) {
        Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>(sortedQueue.getScoreQueue().size());
        for (ScoreSortedQueue.ScoreEntry entry : sortedQueue.getScoreQueue()) {
            tuples.add(new DefaultTypedTuple<>(entry.getValue(), entry.getScore()));
        }
        stringRedisTemplate.opsForZSet().add(zsetKey, tuples);
    }

    public void zadd(String fileScoreKey, double trendingScore, String value) {
        stringRedisTemplate.opsForZSet().addIfAbsent(fileScoreKey, value, trendingScore);
    }

    // 使用 Lua 脚本实现原子性操作
    String ZSET_ADD_ADD_LIMIT =
            "redis.call('ZADD', KEYS[1], ARGV[1], ARGV[2])\n" +
                    "redis.call('ZREM', KEYS[1], ARGV[1], ARGV[4])\n" +
                    "local current = redis.call('ZCARD', KEYS[1])\n" +
                    "if current  > tonumber(ARGV[3]) then\n" +
                    "    redis.call('ZREMRANGEBYRANK', KEYS[1], 0, 0)\n" +
                    "end\n" +
                    "return nil";

    public void addWithLimit(String key, double score, String value, int maxCount, String oldValue) {
        // 计算脚本的 SHA1 值
        String scriptSha1 = calcSha1(ZSET_ADD_ADD_LIMIT);
        // 检查脚本是否已经缓存
        List<Boolean> exists = stringRedisTemplate.execute(
                (redisConnection) -> redisConnection.scriptExists(scriptSha1),
                true);
        // 创建 Redis 脚本对象

//        DefaultRedisScript<Void> redisScript = new DefaultRedisScript<>(ZSET_ADD_ADD_LIMIT, Void.class);
//        stringRedisTemplate.execute(redisScript, List.of(key), String.valueOf(score), value, String.valueOf(maxCount));

        // 检查 Redis 中是否已缓存此脚本
        if (exists != null && exists.get(0)) {
            // 脚本已经缓存，使用 EVALSHA 执行
            stringRedisTemplate.execute(
                    (redisConnection) -> redisConnection.evalSha(scriptSha1,
                            ReturnType.BOOLEAN, 1, key.getBytes(), String.valueOf(score).getBytes(), value.getBytes(), String.valueOf(maxCount).getBytes(), oldValue.getBytes()),
                    true);
        } else {
            stringRedisTemplate.execute(
                    (redisConnection) -> redisConnection.eval(ZSET_ADD_ADD_LIMIT.getBytes(StandardCharsets.UTF_8),
                            ReturnType.BOOLEAN, 1, key.getBytes(), String.valueOf(score).getBytes(), value.getBytes(), String.valueOf(maxCount).getBytes(), oldValue.getBytes()),
                    true);
        }

    }

    public void zremrangeByRank(String zsetKey, long start, long end) {
        stringRedisTemplate.opsForZSet().removeRange(zsetKey, start, end);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevrangeWithScores(String fileScoreKey, long offset, long i) {
        return stringRedisTemplate.opsForZSet().reverseRangeWithScores(fileScoreKey, offset, i);
    }


    public Long getZSetIndex(String zsetKey, String value) {
        return stringRedisTemplate.opsForZSet().rank(zsetKey, value);
    }

    public Long getZSetReverseIndex(String zsetKey, String value) {
        return stringRedisTemplate.opsForZSet().reverseRank(zsetKey, value);
    }

    public void zrem(String zsetKey, String value) {
        stringRedisTemplate.opsForZSet().remove(zsetKey, value);
    }

    public Long getExpireTime(String key, TimeUnit timeUnit) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return redisTemplate.getExpire(key, timeUnit == null ? TimeUnit.SECONDS : timeUnit);
    }

    public Long increment(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return stringRedisTemplate.opsForValue().increment(key);
    }

    public void decrementHashAndDel(String key, String field) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(field)) {
            return;
        }
        DefaultRedisScript<Void> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_HASH_DECREMENT_AND_DEL);
        redisScript.setResultType(Void.class);
        stringRedisTemplate.execute(redisScript, List.of(key), field);
    }

    public void decrementHash(String key, String field) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(field)) {
            return;
        }
        DefaultRedisScript<Void> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(LUA_SCRIPT_HASH_DECREMENT);
        redisScript.setResultType(Void.class);
        stringRedisTemplate.execute(redisScript, List.of(key), field);
    }

    public Map<String, Object> getKeys(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return null;
        }
        List<Object> values = redisTemplate.opsForValue().multiGet(keys);
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        for (int i = 0; i < keys.size(); i++) {
            if (values.get(i) == null) {
                continue;
            }
            result.put(keys.get(i), values.get(i));
        }
        return result;
    }
}
