package com.lx.pl.service;

import cn.hutool.core.lang.Validator;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.lx.pl.client.SendCloudEmailClient;
import com.lx.pl.config.GoogleOAuthLoginProperties;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.entity.LumenChangeRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.VipStandards;
import com.lx.pl.db.mysql.gen.mapper.*;
import com.lx.pl.db.mysql.mapper.PicLumenMapper;
import com.lx.pl.dto.*;
import com.lx.pl.dto.common.Login;
import com.lx.pl.dto.common.LoginOauth;
import com.lx.pl.dto.common.Register;
import com.lx.pl.enums.ClientType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.UploadType;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.exception.UserPasswordNotMatchException;
import com.lx.pl.manager.TokenManager;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.mapper.AiUserDeviceInfoMapper;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.util.FileUtils;
import com.lx.pl.util.IpUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.MyEnvironmentUtils;
import com.lx.pl.vo.LumenChangeRecordVO;
import com.lx.pl.vo.UserLumenDetailVO;
import com.lx.pl.vo.UserResourceDetailVo;
import com.sendcloud.sdk.util.ResponseData;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.bson.types.ObjectId;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.service.ImgUploadCommonService.getFilePath;
import static com.lx.pl.service.QuestionnaireAnswerService.*;

@Service
@Slf4j
public class UserService {
    @Value("${gpt.introduceTokenCount}")
    long introduceTokenCount;
    @Value("${gpt.initCount}")
    long initCount;
    @Value("${gpt.freeCostToken}")
    long freeCostToken;
    @Value("avatar.folder")
    String avatarFolder;
    @Value("thumbnail.avatar.folder")
    String thumbnailAvatarFolder;
    @Value("${sendcloud.api.key}")
    String apiKey;
    @Value("${sendcloud.api.user}")
    String apiUser;
    @Autowired
    TokenManager tokenManager;
    @Autowired
    UserMapper userMapper;
    @Autowired
    PicLumenMapper gptMapper;
    @Autowired
    DivideCommissionRecordMapper divideCommissionRecordMapper;
    @Autowired
    TradeRecordMapper tradeRecordMapper;
    @Autowired
    UserConfigMapper userConfigMapper;
    @Autowired
    RedisService<String> redisService;
    @Autowired
    private AiUserDeviceInfoMapper aiUserDeviceInfoDao;

    @Autowired
    private UserCollectClassifyMapper userCollectClassifyMapper;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    ImgUploadCommonService imgUploadCommonService;
    @Autowired
    SendCloudEmailClient sendCloudEmailClient;
    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private AppleService appleService;

    @javax.annotation.Resource
    GeoIpCountryService geoIpCountryService;

    @Autowired
    CommUserService commUserService;

    @Autowired
    VipService vipStandardsService;
    @Autowired
    SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    EmailService emailService;

    @Lazy
    @Autowired
    private GenService genService;

    @Autowired
    private GoogleOAuthLoginProperties googleOAuthLoginProperties;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ObjectMapper objectMapper;


    private static final String USER_INFO_ENDPOINT = "https://www.googleapis.com/oauth2/v2/userinfo?alt=json";
    @Autowired
    private LumenService lumenService;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ModelService modelService;

    public static String getCurrentDate() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return currentDate.format(formatter);
    }

    public static int getDailyCount(String dailyCountStr) {
        String[] dailyCountInfo = dailyCountStr.split("|");
        return Integer.parseInt(dailyCountInfo[1]);
    }

    @SneakyThrows
    public Login loginOauth(LoginOauth loginOauth, HttpServletRequest request) {
        if (!"google".equalsIgnoreCase(loginOauth.getUser().getPlatform())) {
            throw new ServerInternalException("only support google oauth for now");
        }

        LoginOauth.User googleUser = loginOauth.getUser();

        String jwtToken = loginOauth.getUser().getAccess_token();

        String payLoad = jwtToken.split("\\.")[1];

        payLoad = new String(Base64.decodeBase64URLSafe(payLoad));

        GoogleAuthResp googleAuthResp = JsonUtils.fromString(payLoad, GoogleAuthResp.class);

        QueryWrapper<User> q = new QueryWrapper<>();
        q.eq("google_id", googleAuthResp.getEmail());
        User user = userMapper.selectOne(q);

        if (null == user) {

            //用户为空时 先判断邮箱是否存在 如果存在做更新操作不做新增
            QueryWrapper<User> q1 = new QueryWrapper<>();
            q1.eq("login_name", googleAuthResp.getEmail());
            User userEmail = userMapper.selectOne(q1);
            if (!Objects.isNull(userEmail)) {
                throw new ServerInternalException("email is already in use,please try to log in with your account and password!");
            }

            // 未注册用户，自动创建账号
            user = new User();
            user.setGoogleId(googleAuthResp.getEmail());
            user.setLoginName(googleAuthResp.getEmail());

            if (StringUtil.isNotBlank(googleAuthResp.getName())) {
                user.setUserName(googleAuthResp.getName());
            } else {
                user.setUserName("google_" + generateRandomString(8));
            }

            user.setEmail(googleAuthResp.getEmail());
//      if (StringUtils.isNotBlank(googleAuthResp.getJti())){
//        user.setPassword(googleAuthResp.getJti());
//      }else{
//        user.setPassword(generateRandomString(8));
//      }
            user.setCreateTime(LocalDateTime.now());
            user.setRoleId(101); // 101 普通用户

            String operateIp = IpUtils.getIpAddr(request);
            if (StringUtils.isNotBlank(operateIp)) {
                String country = geoIpCountryService.getCountry(operateIp).get();
                user.setRegistCountry(country);
            }

            if (StringUtil.isNotBlank(googleAuthResp.getPicture())) {
                user.setAvatarUrl(googleAuthResp.getPicture());
                user.setThumbnailAvatarUrl(googleAuthResp.getPicture());
            }
            userMapper.insert(user);

            //增加社区的用户信息
            commUserService.addCommUser(user);
            Login login = loginByThirdChannels(user);
            //增加初始化兼容
            vipStandardsService.dealUserVipStandards(user);

            return login;
        } else {
            // 判断账号是否冻结或者删除
            if (!"0".equals(user.getDelFlag())) {
                throw new ServerInternalException("email deleted or disabled!");
            }
            // 已注册用户
            return loginByThirdChannels(user);
        }
    }

    public Login loginOauthWithCode(LoginOauth loginOauth, HttpServletRequest request) {
        if (!"google".equalsIgnoreCase(loginOauth.getUser().getPlatform())) {
            throw new ServerInternalException("only support google oauth for now");
        }

        String appPlatform = request.getHeader("platform");
        GoogleIdToken.Payload payload = null;
        try {
            String idTokenString = null;
            if (ClientType.web.getValue().equalsIgnoreCase(appPlatform)) {
                idTokenString = getGoogleIdTokenByCode(loginOauth);
            } else if (ClientType.ios.getValue().equalsIgnoreCase(appPlatform)) {
                idTokenString = loginOauth.getUser().getAccess_token();
            }

            if (StringUtils.isNotBlank(idTokenString)) {
                payload = verifyGoogleIdToken(idTokenString, googleOAuthLoginProperties.getWebClientId());
            }
        } catch (Exception e) {
            log.error("Failed to get user information, ", e);
        }

        if (payload == null) {
            throw new ServerInternalException("Login failed. Please try again later");
        }

        //获取用户信息
        String email = payload.getEmail();
        String name = (String) payload.get("name");
        String picture = (String) payload.get("picture");

        if (StringUtils.isBlank(email)) {
            log.error("谷歌登录，未获取到用户邮件信息");
            throw new ServerInternalException("Login failed. Please try again later");
        }

        QueryWrapper<User> q = new QueryWrapper<>();
        q.eq("google_id", email);
        User user = userMapper.selectOne(q);

        if (null == user) {
            //用户为空时 先判断邮箱是否存在 如果存在做更新操作不做新增
            QueryWrapper<User> q1 = new QueryWrapper<>();
            q1.eq("login_name", email);
            User userEmail = userMapper.selectOne(q1);
            if (!Objects.isNull(userEmail)) {
                throw new ServerInternalException("email is already in use,please try to log in with your account and password!");
            }

            // 未注册用户，自动创建账号
            user = new User();
            user.setGoogleId(email);
            user.setLoginName(email);

            if (StringUtil.isNotBlank(name)) {
                user.setUserName(name);
            } else {
                user.setUserName("google_" + generateRandomString(8));
            }

            user.setEmail(email);
            user.setCreateTime(LocalDateTime.now());
            user.setRoleId(101); // 101 普通用户

            String operateIp = IpUtils.getIpAddr(request);
            if (StringUtils.isNotBlank(operateIp)) {
                String country = geoIpCountryService.getCountry(operateIp).get();
                user.setRegistCountry(country);
            }

            if (StringUtil.isNotBlank(picture)) {
                user.setAvatarUrl(picture);
                user.setThumbnailAvatarUrl(picture);
            }
            userMapper.insert(user);

            //增加社区的用户信息
            commUserService.addCommUser(user);
            Login login = loginByThirdChannels(user);
            //增加初始化兼容
            vipStandardsService.dealUserVipStandards(user);

            return login;
        } else {
            // 判断账号是否冻结或者删除
            if (!"0".equals(user.getDelFlag())) {
                throw new ServerInternalException("email deleted or disabled!");
            }
            // 已注册用户
            return loginByThirdChannels(user);
        }
    }

    private GoogleIdToken.Payload verifyGoogleIdToken(String idTokenString, String clientId) {
        try {
            GoogleIdTokenVerifier verifier = new GoogleIdTokenVerifier
                    .Builder(new NetHttpTransport(), new GsonFactory())
                    .setAudience(Collections.singletonList(clientId))
                    .build();
            GoogleIdToken idTokenObj = verifier.verify(idTokenString);
            GoogleIdToken.Payload payload = idTokenObj.getPayload();
            //验证token过期时间
            if (payload.getExpirationTimeSeconds() < System.currentTimeMillis() / 1000) {
                log.error("token expired");
                return null;
            }
            String existKey = payload.getJwtId();
            if (StringUtils.isBlank(existKey)) {
                existKey = DigestUtils.sha256Hex(idTokenString);
            }
            existKey = LogicConstants.GOOGLE_LOGIN_USED_ID_TOKEN_KEY_PREFIX + existKey;
            if (redisService.hasKey(existKey)) {
                log.error("token already used");
                return null;
            }
            redisService.set(existKey, "1", payload.getExpirationTimeSeconds() - System.currentTimeMillis() / 1000);
            return payload;
        } catch (Exception e) {
            log.error("idToken验证异常, ", e);
            return null;
        }
    }

    private String getGoogleIdTokenByCode(LoginOauth loginOauth) {
        try {
            //授权码
            String code = loginOauth.getUser().getCode();
            //授权码重定向地址
            String redirectUri = loginOauth.getUser().getRedirectUri();
            if (StringUtils.isBlank(code) || StringUtils.isBlank(redirectUri)) {
                log.error("code or redirectUri is empty");
                return null;
            }
            //交换token
            GoogleAuthorizationCodeTokenRequest tokenRequest = new GoogleAuthorizationCodeTokenRequest(
                    new NetHttpTransport(),
                    new GsonFactory(),
                    googleOAuthLoginProperties.getWebClientId(),
                    googleOAuthLoginProperties.getWebClientSecret(),
                    code,
                    redirectUri
            );
            GoogleTokenResponse tokenResponse = tokenRequest.execute();
            return tokenResponse.getIdToken();
        } catch (Exception e) {
            log.error("通过code换取token异常, ", e);
            return null;
        }
    }

    public Login login(String account, String password) {
        if (StringUtils.isBlank(account) || StringUtils.isBlank(password)) {
            throw new UserPasswordNotMatchException("Email or password cannot be empty !");
        }

        User user = getUserByAccount(account);

        if (user == null || !password.equals(user.getPassword())) {
            //提示用户名或密码错误
            throw new UserPasswordNotMatchException("Incorrect Email or password or deleted or disabled! ");
        }

        log.info("用户登录，id：{}", user.getId());

        Login login = handleToken(user);

        log.info("用户登录处理结束");

        return login;
    }

    private Login handleToken(User user) {
        if (user.getRoleId() == null) {
            throw new ServerInternalException("No role assigned to this user");
        }

        String token = tokenManager.getTokenByUserId(user.getId());
        if (token == null) {
            //生成一个token，保存用户登录状态
            token = tokenManager.createToken(user.getId(), user.getRoleId());
        }

        String userId = tokenManager.getUserIdByToken(token);
        if (StringUtils.isBlank(userId)) {
            Long expireTime = tokenManager.getKeyExpireTime(String.valueOf(user.getId()));
            tokenManager.createUserIdByToken(token, user.getId(), expireTime, TimeUnit.SECONDS);
        }

        Login loginVo = new Login();
        loginVo.setToken(token);
        loginVo.setUserId(user.getId());
        loginVo.setUserName(user.getUserName());
        loginVo.setLoginName(user.getLoginName());
        loginVo.setEmail(user.getEmail());
        loginVo.setDeptId(user.getDeptId());
        loginVo.setRole(user.getRoleId());
        loginVo.setSex(user.getSex());
        loginVo.setVisibility(user.getVisibility());
        loginVo.setTotalSize(user.getTotalSize());
        loginVo.setUsedSize(user.getUsedSize());
        return loginVo;
    }

    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }

    public User getUserByAccount(String account) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("login_name", account);
        map.put("del_flag", "0");
        List<User> sysUsers = userMapper.selectByMap(map);
        if (sysUsers.size() > 0) {
            return sysUsers.get(0);
        }
        return null;
    }

    public void logout(String token, User user) {
        tokenManager.deleteToken(token, String.valueOf(user.getId()));
        //更新用户的平台设备信息
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getId, user.getId());
        updateWrapper.set(User::getIosDeviceToken, "");
        updateWrapper.set(User::getAndroidDeviceToken, "");
        updateWrapper.set(User::getIosFcmToken, "");
        updateWrapper.set(User::getAndroidFcmToken, "");
        userMapper.update(null, updateWrapper);
    }

    public UserInfo getUserInfo(Long id, String platform) {
        //refreshDailyCount(id);

        User u = getUserById(id);
        //判断用户是否是vip，并进行处理
        vipStandardsService.dealUserVipStandards(u);
        u.setPassword("*");

        UserInfo userInfo = new UserInfo();
        BeanUtils.copyProperties(u, userInfo);
        VipStandards vipStandards = genService.getVipStandards(u);
        if (!Objects.isNull(vipStandards)) {
            userInfo.setTotalCollectNum(vipStandards.getCollectNum());
        }

        //每日免费lumen弹窗控制
        Boolean showDailyLumenDialog = (Boolean) redisService.getDataFromHash(LogicConstants.USER_DAILY_LUMEN_DIALOG_CONTROL_CACHE_KEY, u.getLoginName());
        userInfo.setDailyLumenDialog(showDailyLumenDialog == null ? false : showDailyLumenDialog);

        /**
         * 相关的key不存在，或key 存在，用户已经回答了
         */
        switch (platform) {
            case "web":
                userInfo.setBeAnswer(redisService.isNotExitKeyAndValueInSet(WEB_QUESTIONNAIRE_ANSWER, u.getLoginName()));
                break;
            case "ios":
                userInfo.setBeAnswer(redisService.isNotExitKeyAndValueInSet(IOS_QUESTIONNAIRE_ANSWER, u.getLoginName()));
                break;
            case "android":
                userInfo.setBeAnswer(redisService.isNotExitKeyAndValueInSet(ANDROID_QUESTIONNAIRE_ANSWER, u.getLoginName()));
                break;
        }
        return userInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void register(Register register, HttpServletRequest request) {
        // 尝试获取分布式锁，设置过期时间为5秒，避免死锁
        String lockKey = LogicConstants.USER_REGISTER_LOCK_PREFIX + register.getAccount();
        boolean isLocked = redisService.tryLock(lockKey, register.getAccount(), 5, TimeUnit.SECONDS);
        try {
            if (isLocked) {
                // 检查用户是否已经注册
                User userByAccount = getUserByAccountToRegister(register.getAccount());
                if (userByAccount != null) {
                    throw new ServerInternalException("Email already taken or deleted or disabled! . Please choose another !");
                }

                boolean isEmail = Validator.isEmail(register.getAccount());
                boolean isMobile = Validator.isMobile(register.getAccount());
                if (!isEmail && !isMobile) {
                    throw new ServerInternalException("Use email or Chinese phone number as username !");
                }

//                if (StrUtil.isBlank(register.getValidateCode())) {
//                    throw new ServerInternalException("Enter the verification code !");
//                }

//                String vCode = redisService.get("REGISTER:" + register.getAccount());
//                if (!register.getValidateCode().equals(vCode)) {
//                    throw new ServerInternalException("Invalid verification code !");
//                }

                String userName = "";
                if (StringUtil.isNotBlank(register.getUserName())) {
                    userName = register.getUserName();
                } else {
                    userName = "web_user" + generateRandomString(7);
                }

                LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
                lqw.eq(User::getUserName, userName);
                Long result = userMapper.selectCount(lqw);
                if (result.intValue() > 0) {
                    throw new ServerInternalException("Email already taken. Please choose another !");
                }

                User user = new User();
                user.setLoginName(register.getAccount());
                user.setUserName(userName);
                user.setEmail(register.getAccount());
                user.setPassword(register.getPassword());
                user.setCreateTime(LocalDateTime.now());
                user.setRoleId(101); // 101 普通用户
                user.setVipType(VipType.basic.getValue());
                user.setIp(IpUtils.getIpAddr(request));

                String operateIp = IpUtils.getIpAddr(request);
                if (StringUtils.isNotBlank(operateIp)) {
                    String country = geoIpCountryService.getCountry(operateIp).get();
                    user.setRegistCountry(country);
                }

                userMapper.insert(user);

                //增加社区的用户信息
                commUserService.addCommUser(user);
            }
        } finally {
            // 释放锁
            if (isLocked) {
                redisService.unlock(lockKey, register.getAccount());
            }
        }
    }

    public void resetPassword(String account, String vCode, String password) {
        User userByAccount = getUserByAccount(account);
        if (null == userByAccount) {
            throw new ServerInternalException("User not found or deleted or disabled. Check Email  !");
        }

        /**
         * 第三方登录的用户不支持忘记密码
         */
        if (StringUtil.isBlank(userByAccount.getPassword())) {
            throw new ServerInternalException("The current user is logged in as a third party and cannot support forgotten passwords !");
        }

        String oldVCode = redisService.get("RESET_PASSWORD:" + account);
        if (!vCode.equals(oldVCode)) {
            throw new ServerInternalException("Invalid code. Password reset failed !");
        }

        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", userByAccount.getId());
        updateWrapper.set("password", password);
        updateWrapper.set("login_name", userByAccount.getLoginName());
        updateWrapper.set("update_time", LocalDateTime.now());
        userMapper.update(null, updateWrapper);

        String token = tokenManager.getTokenByUserId(userByAccount.getId());
        if (StringUtil.isNotBlank(token)) {
            logout(token, userByAccount);
        }
    }

    public void resetPasswordSendCode(String account, String platform) {
        boolean isEmail = Validator.isEmail(account);
        boolean isMobile = Validator.isMobile(account);
        if (!isEmail && !isMobile) {
            throw new ServerInternalException("Invalid Email format. Use phone number or emai !");
        }

        User userByAccount = getUserByAccount(account);
        if (null == userByAccount) {
            throw new ServerInternalException("User not found or deleted or disabled! . Check account info  !");
        }

        String codeNum = genCodeNum();
        redisService.set("RESET_PASSWORD:" + account, codeNum, 10, TimeUnit.MINUTES);
        if (isEmail) {
            try {
                if (MyEnvironmentUtils.isStagingEnvironment() && "android".equals(platform)) {
                    sendMail(account, codeNum, "classpath:templatesAndroid/changePasswordTemplate.html", platform);
                } else {
                    sendMail(account, codeNum, "classpath:templates/changePasswordTemplate.html", platform);
                }
            } catch (JsonProcessingException e) {
                throw new ServerInternalException("Failed to send code. Try again later !");
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else if (isMobile) {
            // sendMobileMessage(account, codeNum);
        }
    }

    public void registerSendCode(String account, String platform) {
        boolean isEmail = Validator.isEmail(account);
        boolean isMobile = Validator.isMobile(account);
//    if (!isEmail && !isMobile) {
//      throw new ServerInternalException("用户名格式不正确，只能是手机号或者邮箱");
//    }
        if (!isEmail) {
            throw new ServerInternalException("Invalid loginName format. Email only !");
        }

        String codeNum = genCodeNum();
        redisService.set("REGISTER:" + account, codeNum, 10, TimeUnit.MINUTES);
        if (isEmail) {
            try {
                if (MyEnvironmentUtils.isStagingEnvironment() && "android".equals(platform)) {
                    sendMail(account, codeNum, "classpath:templatesAndroid/registerTemplate.html", platform);
                } else {
                    sendMail(account, codeNum, "classpath:templates/registerTemplate.html", platform);
                }
            } catch (JsonProcessingException e) {
                throw new ServerInternalException("Failed to send code. Try again later !");
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else if (isMobile) {
            // sendMobileMessage(account, codeNum);
        }
    }

    public String genCodeNum() {
        String codeNum = "";
        int[] code = new int[3];
        Random random = new Random();
        //自动生成验证码
        for (int i = 0; i < 4; i++) {
            int num = random.nextInt(10) + 48;
            code[0] = num;
            codeNum += (char) code[0];
        }
        System.out.println(codeNum);
        return codeNum;
    }

    public void sendMail(String mail, String codeNum, String templatePath, String platform) throws IOException {
        String from = "<EMAIL>";
        String fromName = "<EMAIL>";
        String to = mail;
        String subject = "Your verification code is ：" + codeNum;
   /* String html = "【PicLumen】 Your verification code is ：" + "<h2>" + codeNum + "</h2>"
                        + " Just don't tell anyone the code !";*/

        Resource resource = resourceLoader.getResource(templatePath);
        InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
        StringBuilder htmlBuilder = new StringBuilder();
        int c;
        while ((c = reader.read()) != -1) {
            htmlBuilder.append((char) c);
        }
        String html = htmlBuilder.toString().replace("${codeNum}", codeNum);

        log.info("发送邮件请求参数: mail :{} ,codeNum : {} ", mail, codeNum);
        if (MyEnvironmentUtils.isStagingEnvironment() && "android".equals(platform)) {
            emailService.sendAndroidHtmlMessage(to, subject, html);
        } else {
            ResponseData response = sendCloudEmailClient.sendEmail(from, fromName, to, subject, html);
            log.info("调用发送邮件返回信息: {}", JsonUtils.writeToString(response));

            if (200 == response.getStatusCode() && Boolean.TRUE == response.getResult()) {
                log.info("邮件发送成功，接收邮箱为：{}", mail);
            } else {
                throw new ServerInternalException("Failed to send code. Try again later !");
            }
        }


    }

//    public void setConfig(User user, String config) {
//        QueryWrapper<UserConfig> qw = new QueryWrapper<>();
//        qw.eq("user_id", user.getId());
//        UserConfig userConfig = userConfigMapper.selectOne(qw);
//        if (userConfig == null) {
//            userConfig = new UserConfig();
//            userConfig.setUserId(user.getId());
//            userConfig.setUserLoginName(user.getLoginName());
//            userConfig.setUserName(user.getUserName());
//            userConfig.setConfig(config);
//            userConfigMapper.insert(userConfig);
//        } else {
//            userConfig.setConfig(config);
//            userConfigMapper.updateById(userConfig);
//        }
//    }

//    public String getConfig(User user) {
//        QueryWrapper<UserConfig> qw = new QueryWrapper<>();
//        qw.eq("user_id", user.getId());
//        UserConfig userConfig = userConfigMapper.selectOne(qw);
//        if (userConfig == null) {
//            return ""; // return an empty config.
//        }
//        String config = userConfig.getConfig();
//        return config;
//    }

    public Boolean checkLoginName(String loginName) {
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.eq(User::getLoginName, loginName);
        Long result = userMapper.selectCount(lqw);
        return result.intValue() > 0 ? Boolean.FALSE : Boolean.TRUE;
    }


    public Boolean checkUserName(String userName) {
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.eq(User::getUserName, userName);
        Long result = userMapper.selectCount(lqw);
        return result.intValue() > 0 ? Boolean.FALSE : Boolean.TRUE;
    }

    public Boolean uploadUserAvatar(MultipartFile avatarImg, User user) throws IOException {
        try {
            File file = FileUtils.transferToFile(avatarImg, "avatarImg");
            //生成缩略图
            File thumbnailFile = FileUtils.createThumbnail(file);

            String signedUrl = imgUploadCommonService.uploadToOssWithType(file, String.valueOf(user.getId()), UploadType.normal.getValue());
            String thumbnailSignedUrl = imgUploadCommonService.uploadToOssWithType(thumbnailFile, String.valueOf(user.getId()), UploadType.normal.getValue());

            user.setAvatarName(getFilePath(signedUrl));
            user.setThumbnailAvatarName(getFilePath(thumbnailSignedUrl));
            user.setAvatarUrl(signedUrl);
            user.setThumbnailAvatarUrl(thumbnailSignedUrl);
            user.setUpdateBy(user.getLoginName());
            user.setUpdateTime(LocalDateTime.now());

            userMapper.updateById(user);
            //更新社区用户信息
            commUserService.updateUserMessage(user.getId(), "", user.getUserName(), user.getAvatarUrl());

            //对于临时文件处理完后，进行删除
            file.delete();
            thumbnailFile.delete();

        } catch (Exception e) {
            log.error("用户：{} 上传头像失败,报错信息：{}", user.getLoginName(), JsonUtils.writeToString(e));
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean updateUserInfo(UserInfo userInfo, User user) throws JsonProcessingException {

        try {
            User user1 = new User();
            user1.setUserName(userInfo.getUserName());
            user1.setSex(userInfo.getSex());
            user1.setClearPrompt(userInfo.getClearPrompt());
            user1.setVisibility(userInfo.getVisibility());
            user1.setLocalLang(userInfo.getLocalLang());
            user1.setUpdateBy(user.getLoginName());
            user1.setIntroduction(userInfo.getIntroduction());
            user1.setUserConfig(userInfo.getUserConfig());
            user1.setUpdateTime(LocalDateTime.now());
            user1.setIosDeviceToken(userInfo.getIosDeviceToken());
            user1.setAndroidDeviceToken(userInfo.getAndroidDeviceToken());
            user1.setIosFcmToken(userInfo.getIosFcmToken());
            user1.setAndroidFcmToken(userInfo.getAndroidFcmToken());
            user1.setId(user.getId());
            userMapper.updateById(user1);

            //更新社区用户信息

            commUserService.updateUserMessage(user.getId(), userInfo.getIntroduction(), userInfo.getUserName(), userInfo.getAvatarUrl());
        } catch (Exception e) {
            log.error("修改用户信息失败：{}", JsonUtils.writeToString(e));
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    public Login appleLogin(AppleLoginDTO appleLoginDTO, HttpServletRequest request) {
        String identityToken = appleLoginDTO.getIdentityToken();

        try {
            // 解析 Apple Token
            String firstDate = new String(org.apache.commons.codec.binary.Base64.decodeBase64(identityToken.split("\\.")[0]), "UTF-8");
            String claim = new String(org.apache.commons.codec.binary.Base64.decodeBase64(identityToken.split("\\.")[1]), "UTF-8");

            JsonNode firstDateNode = new ObjectMapper().readTree(firstDate);
            String kid = firstDateNode.get("kid").asText();

            JsonNode claimNode = new ObjectMapper().readTree(claim);
            String aud = claimNode.get("aud").asText();
            String sub = claimNode.get("sub").asText();
            String email = claimNode.has("email") ? claimNode.get("email").asText() : null;

            // 获取 Apple 的公钥
            PublicKey publicKey = appleService.getPublicKey(kid);
            if (Objects.isNull(publicKey)) {
                log.error("无法获取 Apple 公钥");
                throw new RuntimeException("Apple authorization login failed, please reauthorize");
            }

            // 验证 Apple 登录授权码
            boolean isValid = appleService.verifyAppleLoginCode(publicKey, identityToken, aud, sub);
            if (!isValid) {
                throw new RuntimeException("Apple authorization login failed, please reauthorize");
            }

            // 查询用户是否存在
            LambdaQueryWrapper<User> qw = new LambdaQueryWrapper<>();
            qw.eq(User::getAppleId, sub);
            User user = userMapper.selectOne(qw);

            if (Objects.isNull(user)) {
                // 用户不存在，创建新用户
                user = new User();
                user.setAppleId(sub);

                // 判断邮箱是否为空，为空则生成随机字符串
                if (StringUtil.isBlank(email)) {
                    user.setLoginName(generateRandomString(15) + "_IOS");
                } else {
                    user.setEmail(email);
                    user.setLoginName(email + "_IOS");
                }

                user.setUserName(appleLoginDTO.getUserName() != null && !appleLoginDTO.getUserName().isEmpty()
                        ? appleLoginDTO.getUserName()
                        : "ios_user" + generateRandomString(7));
                user.setCreateTime(LocalDateTime.now());
                user.setDelFlag("0");
                user.setRoleId(101); // 101 - 普通用户

                String operateIp = IpUtils.getIpAddr(request);
                if (StringUtils.isNotBlank(operateIp)) {
                    String country = geoIpCountryService.getCountry(operateIp).get();
                    user.setRegistCountry(country);
                }
                userMapper.insert(user);

                //增加社区的用户信息
                commUserService.addCommUser(user);
            }

            // 判断此账号邮箱是否禁用
            if (!"0".equals(user.getDelFlag())) {
                throw new ServerInternalException("email deleted or disabled!");
            }

            // 登录用户
            return loginByThirdChannels(user);

        } catch (JsonProcessingException e) {
            // 捕获 JSON 解析异常
            log.error("解析 Apple Token 载荷失败", e);
            throw new RuntimeException("解析 Apple Token 载荷失败");

        } catch (UnsupportedEncodingException e) {
            // 捕获编码异常
            log.error("编码错误", e);
            throw new RuntimeException("Apple authorization login failed due to encoding error");

        } catch (Exception e) {
            // 捕获其他所有异常
            log.error("Apple 登录失败", e);
            throw new RuntimeException("Apple authorization login failed, please reauthorize");
        }
    }


    public Boolean registerCheckVCode(String loginName, String inviteCode) {
        String vCode = redisService.get("REGISTER:" + loginName);
        if (!inviteCode.equals(vCode)) {
            throw new ServerInternalException("Invalid verification code !");
        }
        return Boolean.TRUE;
    }

    public Boolean resetCheckVCode(String loginName, String inviteCode) {
        String vCode = redisService.get("RESET_PASSWORD:" + loginName);
        if (!inviteCode.equals(vCode)) {
            throw new ServerInternalException("Invalid verification code !");
        }
        return Boolean.TRUE;
    }

    public Boolean userAccountDeletion(UserDeleteDto userDeleteDto, User user, String token) {//

        // 检查反馈邮箱是否为空或格式错误
        boolean isEmail = Validator.isEmail(userDeleteDto.getFeedbackEmail());
        if (!isEmail) {
            throw new ServerInternalException("please use the correct email format");
        }

        // 检查用户是否存在及其删除状态
        if (Objects.isNull(user) || !"0".equals(user.getDelFlag())) {
            throw new ServerInternalException("This user does not exist or has been deleted");
        }

        // 更新用户删除标志和反馈邮箱
        user.setDelFlag("1");
        user.setFeedbackEmail(userDeleteDto.getFeedbackEmail());
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 删除 Redis 中的用户缓存信息
        logout(token, user);

        // 返回操作成功标志
        return true;
    }

    public User getUserByAccountToRegister(String account) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("login_name", account);
        List<User> sysUsers = userMapper.selectByMap(map);
        if (sysUsers.size() > 0) {
            return sysUsers.get(0);
        }
        return null;
    }

    public Login loginByThirdChannels(User user) {
        log.info("用户登录，id：{}", user.getId());

        Login login = handleToken(user);

        log.info("用户登录处理结束");

        return login;
    }

    // 生成任意位随机字符串的方法
    private String generateRandomString(int length) {
        return UUID.randomUUID().toString().replaceAll("-", "").substring(0, length);
    }

    private final Cache<String, String> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(2, TimeUnit.HOURS)
            .build();

    public String queryUserIdByName(String userLoginName) {
        String key = "UID:" + userLoginName;
        String userId = cache.getIfPresent(key);
        if (userId == null) {
            userId = redisService.get(key);
        }
        if (userId == null) {
            QueryWrapper<User> qw = new QueryWrapper<>();
            qw.select("id").eq("login_name", userLoginName).eq("del_flag", 0);
            User user = userMapper.selectOne(qw);
            if (user != null) {
                redisService.set(key, String.valueOf(user.getId()), 2L, TimeUnit.HOURS);
                cache.put(key, String.valueOf(user.getId()));
                userId = String.valueOf(user.getId());
            } else {
                userId = "none";
            }
        }
        return userId;
    }


    public void updateUsedSize(Long userId, Long size) {
        LambdaUpdateWrapper<User> uw = new LambdaUpdateWrapper<>();
        uw.eq(User::getId, userId);
        uw.set(User::getUsedSize, size);
        userMapper.update(null, uw);
    }

    public void updateUsedCollectNum(Long userId, Integer num) {
        LambdaUpdateWrapper<User> uw = new LambdaUpdateWrapper<>();
        uw.eq(User::getId, userId);
        uw.set(User::getUsedCollectNum, num);
        userMapper.update(null, uw);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserVipInfo(SubscriptionCurrent current, Long userId) {
        log.info("validHighSubscriptionsFromDb {} {}", current.getLoginName(), current);

//        long collectSize = 500;
//        if (VipType.standard.getValue().equals(current.getPlanLevel())) {
//            collectSize = 5000;
//        } else if (VipType.pro.getValue().equals(current.getPlanLevel())) {
//            // 50GB
//            collectSize = 50000;
//        }
        LambdaUpdateWrapper<User> uw = new LambdaUpdateWrapper<>();
        uw.eq(User::getId, userId);
        uw.set(User::getVipType, current.getPlanLevel());
//        uw.set(User::getCurrentSubscription, current.getSubscriptionId());
        uw.set(User::getPriceInterval, current.getPriceInterval());
//        uw.set(User::getVipBeginTime, startDate);
        uw.set(User::getVipEndTime, current.getVipEndTime());
//        uw.set(User::getTotalCollectNum, collectSize);
//        uw.set(User::getTotalSize, totalSize);
        userMapper.update(null, uw);
        commUserService.updateUserVip(current, userId);
    }

    public User getByLoginName(String logingName) {
        QueryWrapper<User> qw = new QueryWrapper<>();
        qw.eq("login_name", logingName);
        return userMapper.selectOne(qw);
    }


    public UserMessageDetail getUserMessageDetail(User user) {
        UserMessageDetail userMessageDetail = new UserMessageDetail();

        //加入社区用户的信息
        userMessageDetail.setCommUser(commUserService.findUserByUserIdAndFollow(user.getId(), user));
        //加入lumen的信息
        userMessageDetail.setUserLumens(lumenService.getUserLumens(user));

        return userMessageDetail;
    }

    public UserLumenDetailVO getUserCurrentLumen(User user) {
        UserLumenDetailVO userLumenDetailVO = new UserLumenDetailVO();
        userLumenDetailVO.setUserId(user.getId());
        userLumenDetailVO.setLoginName(user.getLoginName());
        userLumenDetailVO.setRemainingAvailableLumen(lumenService.getUserSurplusLumens(user));

        return userLumenDetailVO;
    }

    public CommPageInfo<LumenChangeRecordVO> lumenChangeRecord(User user, String lastRecordId, Integer pageSize) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(user.getId())
                .and("happenTimeSec")
                .gte(Instant.now().getEpochSecond() - 30 * 24 * 60 * 60)
                .lte(Instant.now().getEpochSecond()));
        if (StringUtils.isNotBlank(lastRecordId)) {
            query.addCriteria(Criteria.where("id").lt(new ObjectId(lastRecordId)));
        }
        //基于发生时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "happenTimeSec"));
        query.limit(pageSize);
        List<LumenChangeRecord> recordList = mongoTemplate.find(query, LumenChangeRecord.class);
        List<LumenChangeRecordVO> recordVOList = null;
        String currentLastId = "";
        if (CollectionUtils.isNotEmpty(recordList)) {
            recordVOList = new ArrayList<>();
            for (LumenChangeRecord record : recordList) {
                recordVOList.add(LumenChangeRecordVO.buildRecordVO(record));
            }
            currentLastId = recordList.get(recordList.size() - 1).getId();
        }

        return buildLumenRecordPageInfo(pageSize, recordVOList, currentLastId);
    }

    private CommPageInfo<LumenChangeRecordVO> buildLumenRecordPageInfo(Integer pageSize, List<LumenChangeRecordVO> recordVOList, String currentLastId) {
        CommPageInfo<LumenChangeRecordVO> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(recordVOList == null ? Collections.emptyList() : recordVOList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(currentLastId);
        return commPageInfo;
    }

    public void dailyLumenDialogControl(User user, boolean showDialog) {
        redisService.putDataToHash(LogicConstants.USER_DAILY_LUMEN_DIALOG_CONTROL_CACHE_KEY, user.getLoginName(), showDialog);
    }

    public Boolean userAccountDeletionV2(String feedbackEmail, User user, String token) {

        // 检查反馈邮箱是否为空或格式错误
        boolean isEmail = Validator.isEmail(feedbackEmail);
        if (!isEmail) {
            throw new LogicException(LogicErrorCode.EMAIL_FORMAT_ERROR);
        }

        // 检查用户是否存在及其删除状态
        if (Objects.isNull(user) || !"0".equals(user.getDelFlag())) {
            throw new LogicException(LogicErrorCode.USER_ACCOUNT_DELETED_OR_NOT_EXIST);
        }

        if (subscriptionCurrentService.hasRenewSubscription(user.getId())) {
            throw new LogicException(LogicErrorCode.HAS_RENEWAL_SUBSCRIBER);
        }

        // 更新用户删除标志和反馈邮箱
        user.setDelFlag("1");
        user.setFeedbackEmail(feedbackEmail);
        user.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 删除 Redis 中的用户缓存信息
        logout(token, user);

        // 返回操作成功标志
        return true;
    }

    public Boolean tokenVerify(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (StringUtils.isBlank(token)) {
            return false;
        }

        return redisService.hasKey(token);
    }

    public UserResourceDetailVo getUserResourceDetail(User user,String platform) throws IOException {

        UserResourceDetailVo userResourceDetailVo = new UserResourceDetailVo();
        userResourceDetailVo.setRemainingAvailableLumen(lumenService.getUserSurplusLumens(user));
        Set<String> allModelIds = new HashSet<>();
        List<UserModelRightsCacheDTO> allUserModelRights = modelService.getAllUserModelRights(user.getLoginName());
        if (CollectionUtils.isNotEmpty(allUserModelRights)) {
            allModelIds = allUserModelRights.stream().map(UserModelRightsCacheDTO::getModelId).collect(Collectors.toSet());
        }
        // toMap
        Map<String, UserModelRightsCacheDTO> allUserModelRightsMap = allUserModelRights.stream().collect(Collectors.toMap(UserModelRightsCacheDTO::getModelId, Function.identity()));

        Map<String, Object>  notFinishTaskFreeTrialList = redisService.getHashAsMap(LogicConstants.USER_NOT_FINISH_TASK_MODEL_FREE_TRIAL_KEY + user.getLoginName());
        if (notFinishTaskFreeTrialList != null) {
            allModelIds.addAll(notFinishTaskFreeTrialList.keySet());
        }
        Map<String, Object>  notFinishTaskPayTrialList = redisService.getHashAsMap(LogicConstants.USER_NOT_FINISH_TASK_MODEL_PAY_TRIAL_KEY + user.getLoginName());
        if (notFinishTaskPayTrialList != null) {
            allModelIds.addAll(notFinishTaskPayTrialList.keySet());
        }
        List<ModelUsedDto> modelUsedDtoList = new ArrayList<>();
        for (String allModelId : allModelIds) {
            ModelUsedDto modelUsedDto = new ModelUsedDto();
            modelUsedDto.setModelId(allModelId);
            modelUsedDto.setUsedFreeTrial(0);
            modelUsedDto.setUsedPayTrial(0);
            if (notFinishTaskFreeTrialList != null && notFinishTaskFreeTrialList.containsKey(allModelId) && notFinishTaskFreeTrialList.get(allModelId) != null ) {
                modelUsedDto.setUsedFreeTrial((Integer) notFinishTaskFreeTrialList.get(allModelId));
            }
            if (notFinishTaskPayTrialList != null && notFinishTaskPayTrialList.containsKey(allModelId) && notFinishTaskPayTrialList.get(allModelId) != null) {
                modelUsedDto.setUsedPayTrial((Integer) notFinishTaskPayTrialList.get(allModelId));
            }
            if (allUserModelRightsMap.containsKey(allModelId)) {
                modelUsedDto.setUsedFreeTrial(modelUsedDto.getUsedFreeTrial() + allUserModelRightsMap.get(allModelId).getUsedFreeTrial());
                modelUsedDto.setUsedPayTrial(modelUsedDto.getUsedPayTrial() + allUserModelRightsMap.get(allModelId).getUsedPayTrial());
            }
            modelUsedDtoList.add(modelUsedDto);
        }
        userResourceDetailVo.setModelUsedList(modelUsedDtoList);
        return userResourceDetailVo;
    }
}
