package com.lx.pl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.lx.pl.config.CosConfig;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.db.mysql.community.activity.CommActivity;
import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.db.mysql.gen.mapper.*;
import com.lx.pl.dto.*;
import com.lx.pl.dto.generic.ListInfo;
import com.lx.pl.enums.*;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.RandomUtils;
import com.lx.pl.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.service.CosService.buildFileNameKey;
import static com.lx.pl.service.GenService.USER_TODAY_CREATE_IMG_NUMS;

@Service
@Slf4j
public class ImgService {

    public static final String USER_PUBLIC_IMG_NUMS = "user_public_img_nums";

    @Value("${image.download.prefix}")
    String imageDownloadPrefix;

    @Value("${image.storage.path.prefix}")
    String storagePathPrefix;

    @Value("${opex.loginName}")
    String opexLoginName;

    @Autowired
    ImageGenRecordMapper imageGenRecordMapper;

    @Autowired
    UserPromptFileRelMapper userPromptFileRelMapper;

    @Autowired
    PromptFileMapper promptFileMapper;

    @Autowired
    PromptRecordMapper promptRecordMapper;

    @Autowired
    RedisService<String> redisService;

    @Autowired
    HomeFileMapper homeFileMapper;

    @Autowired
    UserLikeMapper userLikeMapper;

    @Autowired
    UserCollectMapper userCollectMapper;

    @Autowired
    UserMapper userMapper;

    @Autowired
    GenService genService;

    @Autowired
    LoadBalanceService loadBalanceService;

    @Autowired
    ExploreFileMapper exploreFileMapper;

    @Autowired
    UserReportMapper userReportMapper;

    @Autowired
    PublicFileReviewMapper publicFileReviewMapper;

    @Autowired
    UserCollectClassifyMapper userCollectClassifyMapper;

    @Autowired
    private UserService userService;
    @Autowired
    private ImgUploadCommonService imgUploadCommonService;
    @Autowired
    private CosConfig cosConfig;
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private PromptFileService promptFileService;

    @Resource
    private  CommActivityMapper commActivityMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private  PublicFileReviewService publicFileReviewService;

    private static final Executor THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime()
            .availableProcessors(),
            Runtime.getRuntime()
                    .availableProcessors() * 2, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(2000),
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
    public static String ExploreFileIds = "EXPLORE_FILE_IDS";

    public PromptPageInfo<PromptFileRecord> genHistoryList(Integer pageNum, Integer pageSize, String vagueKey, User user, Boolean notWeb) throws JsonProcessingException {

        String loginName = user.getLoginName();
        List<String> notFinishTaskList = new ArrayList<>();
        //获取未完成任务信息
        List<String> userNotFinishTaskList = redisService.getAllKeysFromHash(user.getLoginName());
        if (!CollectionUtils.isEmpty(userNotFinishTaskList)) {
            for (String userNotFinishTask : userNotFinishTaskList) {
                notFinishTaskList.add(userNotFinishTask);
            }
        }

        //模糊分页查询任务
        LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<>();
        vagueKey = StringUtils.isNotBlank(vagueKey) ? vagueKey.trim() : "";
        if (StringUtils.isNotBlank(vagueKey)) {
            qw.like(PromptRecord::getPrompt, vagueKey);
        }
        qw.eq(PromptRecord::getDel, Boolean.FALSE);
        qw.eq(PromptRecord::getLoginName, loginName);
        if (!CollectionUtils.isEmpty(notFinishTaskList)) {
            qw.notIn(PromptRecord::getMarkId, notFinishTaskList);
        }
        //判断用户是否是非vip
        Boolean notVip = VipType.basic.getValue().equals(user.getVipType());
        if (notVip) {
            qw.gt(PromptRecord::getCreateTime, LocalDateTime.now().minusDays(30));
        }
        qw.isNotNull(PromptRecord::getPromptId);
        qw.orderByDesc(PromptRecord::getCreateTime);
        qw.orderByDesc(PromptRecord::getId);

        IPage<PromptRecord> page = promptRecordMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<PromptRecord> records = page.getRecords();

        List<PromptFileRecord> promptFileRecordList = new ArrayList<>();

        //对图片数据进行封装
        if (!CollectionUtils.isEmpty(records)) {
            List<String> promptIdList = records.stream().map(PromptRecord -> PromptRecord.getPromptId()).collect(Collectors.toList());

            LambdaQueryWrapper<PromptFile> prq = new LambdaQueryWrapper<>();
            prq.in(PromptFile::getPromptId, promptIdList);
            prq.eq(PromptFile::getLoginName, loginName);
            prq.eq(PromptFile::getDel, Boolean.FALSE);

            List<PromptFile> promptFileList = promptFileMapper.selectList(prq);

            if (!CollectionUtils.isEmpty(promptFileList)) {
                //对同一个promptId分组
                Map<String, List<PromptFile>> userMap = promptFileList.stream().collect(Collectors.groupingBy(PromptFile::getPromptId));

                //处理任务数据
                for (PromptRecord promptRecord : records) {
                    PromptFileRecord promptFileRecord = new PromptFileRecord();
                    BeanUtils.copyProperties(promptRecord, promptFileRecord, "genInfo");
                    promptFileRecord.setGenInfo(JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class));
                    promptFileRecord.setAvatar(user.getThumbnailAvatarUrl());
                    promptFileRecord.setUserName(user.getUserName());
                    promptFileRecord.setCreateTimestamp(promptRecord.getCreateTime());
                    promptFileRecord.setDuration(promptRecord.getDuration());

                    List<PromptFile> promptFiles = userMap.containsKey(promptRecord.getPromptId()) ? userMap.get(promptRecord.getPromptId()) : new ArrayList<>();
                    if (!CollectionUtils.isEmpty(promptFiles)) {
                        List<PromptFileRecord.ImgUrl> imgUrlList = new ArrayList<>();

                        for (PromptFile promptFile : promptFiles) {
                            //对图片数据进行转换
                            PromptFileRecord.ImgUrl imgUrl = new PromptFileRecord.ImgUrl();
                            imgUrl.setId(promptFile.getId());
                            imgUrl.setImgName(promptFile.getFileName());
                            imgUrl.setImgUrl(promptFile.getFileUrl());
                            imgUrl.setThumbnailName(promptFile.getThumbnailName());
                            imgUrl.setThumbnailUrl(promptFile.getThumbnailUrl());
                            imgUrl.setHighThumbnailName(promptFile.getHighThumbnailName());
                            imgUrl.setHighThumbnailUrl(promptFile.getHighThumbnailUrl());
                            imgUrl.setMiniThumbnailUrl(promptFile.getMiniThumbnailUrl());
                            imgUrl.setHighMiniUrl(promptFile.getHighMiniUrl());
                            imgUrl.setSensitive(promptFile.getSensitiveMessage());
                            imgUrl.setRealWidth(promptFile.getWidth());
                            imgUrl.setRealHeight(promptFile.getHeight());
                            imgUrl.setOriginCreate(promptRecord.getOriginCreate());
                            imgUrl.setIsPublic(promptFile.getIsPublic());
                            imgUrl.setRejectionContent(promptFile.getRejectionContent());
                            imgUrl.setCollectNums(promptFile.getCollectNums());
                            imgUrl.setVideoUrl(promptFile.getVideoUrl());
                            imgUrl.setDuration(promptFile.getDuration());
                            imgUrlList.add(imgUrl);
                        }

                        //将图片封装到任务中
                        promptFileRecord.setImg_urls(imgUrlList);
                    }
                    //获取结果数据
                    promptFileRecordList.add(promptFileRecord);
                }
            }
        }

        PromptPageInfo<PromptFileRecord> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(promptFileRecordList);
        promptPageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        return promptPageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByFilename(String imgName, String promptId, User user) {
        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());
        try {
            lock.lock();
            log.info("用户:{} 删除图片：{}", user.getLoginName(), imgName);
            PromptFile promptFile = promptFileService.lambdaQuery()
                    .eq(PromptFile::getPromptId, promptId)
                    .eq(PromptFile::getFileName, imgName)
                    .eq(PromptFile::getLoginName, user.getLoginName())
                    .one();
            if (Objects.isNull(promptFile)) {
                log.info("删除失败，请重试, 未查询到对应的图片{} {}", imgName, user.getLoginName());
                return true;
            }

            //对任务进行判断，如果已经没有图片了，则逻辑删除任务
            LambdaQueryWrapper<PromptFile> pqw = new LambdaQueryWrapper<>();
            pqw.eq(PromptFile::getPromptId, promptId);
            pqw.eq(PromptFile::getLoginName, user.getLoginName());
            pqw.eq(PromptFile::getDel, Boolean.FALSE);
            pqw.ne(PromptFile::getId, promptFile.getId());
            Long promptFiles = promptFileMapper.selectCount(pqw);

            if (Objects.isNull(promptFiles) || promptFiles.intValue() == 0) {
                LambdaUpdateWrapper<PromptRecord> prq = new LambdaUpdateWrapper<>();
                prq.eq(PromptRecord::getPromptId, promptId);
                prq.eq(PromptRecord::getLoginName, user.getLoginName());
                prq.eq(PromptRecord::getDel, Boolean.FALSE);
                prq.set(PromptRecord::getUpdateTime, LocalDateTime.now());
                prq.set(PromptRecord::getUpdateBy, user.getLoginName());
                prq.set(PromptRecord::getDel, Boolean.TRUE);
                promptRecordMapper.update(null, prq);
            }

            LambdaQueryWrapper<UserCollect> ucqw = new LambdaQueryWrapper<>();
            ucqw.eq(UserCollect::getPromptId, promptId);
            ucqw.eq(UserCollect::getFileName, imgName);
            ucqw.eq(UserCollect::getLoginName, user.getLoginName());
            Long aLong = userCollectMapper.selectCount(ucqw);

            // 如果没有收藏,删除对应的审核记录
            if (aLong <= 0) {
                LambdaQueryWrapper<PublicFileReview> ruw = new LambdaQueryWrapper<>();
                ruw.eq(PublicFileReview::getPromptId, promptId);
                ruw.eq(PublicFileReview::getFileName, imgName);
                ruw.eq(PublicFileReview::getLoginName, user.getLoginName());
                publicFileReviewMapper.delete(ruw);
            }

            if (promptFile.getCollectNums() == 0) {
                List<String> urlList = addImgUrl(Lists.newArrayList(promptFile));
                promptFileService.lambdaUpdate()
                        .eq(PromptFile::getId, promptFile.getId())
                        .eq(PromptFile::getLoginName, user.getLoginName())
                        .remove();
                imgUploadCommonService.deleteObjectsWithUrlListAsync(urlList);
            } else {
                promptFileService.lambdaUpdate()
                        .eq(PromptFile::getId, promptFile.getId())
                        .eq(PromptFile::getLoginName, user.getLoginName())
                        .set(PromptFile::getDel, Boolean.TRUE)
                        .set(PromptFile::getUpdateTime, LocalDateTime.now())
                        .update();
            }
        } catch (Exception e) {
            log.error("用户：{}，删除图片：{}，失败", user.getLoginName(), imgName, e);
            return Boolean.FALSE;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return Boolean.TRUE;
    }

    private List<String> addImgUrl(List<PromptFile> promptFiles) {
        if (CollUtil.isEmpty(promptFiles)) {
            return Collections.emptyList();
        }
        List<String> urlList = new ArrayList<>();
        for (PromptFile promptFile : promptFiles) {
            if (promptFile.getFileUrl() != null) {
                urlList.add(promptFile.getFileUrl());
            }
            if (promptFile.getThumbnailUrl() != null) {
                urlList.add(promptFile.getThumbnailUrl());
            }
            if (promptFile.getHighThumbnailUrl() != null) {
                urlList.add(promptFile.getHighThumbnailUrl());
            }
            if (promptFile.getMiniThumbnailUrl() != null) {
                urlList.add(promptFile.getMiniThumbnailUrl());
            }
            if (promptFile.getHighMiniUrl() != null) {
                urlList.add(promptFile.getHighMiniUrl());
            }
            if (StringUtils.isNotEmpty(promptFile.getVideoUrl())) {
                urlList.add(promptFile.getVideoUrl());
            }
        }
        return urlList;
    }

    private void addFileUrlToList(PromptFile promptFile, List<String> urlList) {
        if (StrUtil.isNotBlank(promptFile.getFileUrl())) {
            urlList.add(promptFile.getFileUrl());
        }
        if (StrUtil.isNotBlank(promptFile.getThumbnailUrl())) {
            urlList.add(promptFile.getThumbnailUrl());
        }
        if (StrUtil.isNotBlank(promptFile.getHighThumbnailUrl())) {
            urlList.add(promptFile.getHighThumbnailUrl());
        }
    }

    public ListInfo<ImageGenRecord> feed(Integer pageNum, Integer pageSize, String prefer, Long id) {
        // TODO 先用测试账号的生图历史代替社区feed
        LocalDateTime dateTime = LocalDateTime.now();
        QueryWrapper<ImageGenRecord> qw = new QueryWrapper<>();
        qw.lt("create_time", dateTime);
        qw.orderByDesc("create_time");
        qw.eq("user_id", 1089);
        qw.isNotNull("file_name");
        IPage<ImageGenRecord> page = imageGenRecordMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<ImageGenRecord> records = page.getRecords();

        addImageDownloadPath(records);
        addThumbnailSuffix(records);

        long total = page.getTotal();

        ListInfo<ImageGenRecord> r = new ListInfo<>();
        r.setList(records);
        r.setTotal(total);
        return r;
    }

    private void addImageDownloadPath(List<ImageGenRecord> records) {
        for (ImageGenRecord i : records) {
            i.setDownloadUrlPrefix(imageDownloadPrefix);
        }
    }

    private void addThumbnailSuffix(List<ImageGenRecord> records) {
        for (ImageGenRecord i : records) {
            i.setThumbnailSuffix("_s");
        }
    }

    @Data
    public static class ImgDetail {

        String filename;
        String nsfwInfo;
    }

    public PromptPageInfo<HomeFileResult> getExploreList(Integer pageNum, Integer pageSize, String vagueKey, String collationName, User user, Boolean notWeb) throws JsonProcessingException {
        //判断当前操作是否是查询用户点赞过的图片
        Boolean userLikeFlag = "Like".equals(collationName);
        List<String> opexLoginNameList = new ArrayList<>();
        if (StringUtil.isNotBlank(opexLoginName)) {
            opexLoginNameList = Arrays.stream(opexLoginName.split(",")).collect(Collectors.toList());
        }
        //去掉搜索关键字的前置，后置空格
        vagueKey = StringUtils.isNotBlank(vagueKey) ? vagueKey.trim() : "";
        // 根据promptId进行分页查询
        Page<PromptFilePageResult> page = new Page<>(pageNum, pageSize);
        IPage<PromptFilePageResult> iPage = promptFileMapper.getPromptFileList(page, user.getLoginName(), vagueKey, collationName, opexLoginNameList);
        List<PromptFilePageResult> records = iPage.getRecords();

        /**
         * 查询当前用户是否对图片进行了点赞
         */
        Map<String, String> userHasLikeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(records) && !userLikeFlag) {
            List<String> promptIds = records.stream().map(PromptFilePageResult::getPromptId).collect(Collectors.toList());
            LambdaQueryWrapper<UserLike> ul = new LambdaQueryWrapper<>();
            ul.eq(UserLike::getLoginName, user.getLoginName());
            ul.in(UserLike::getPromptId, promptIds);

            List<UserLike> userLikeList = userLikeMapper.selectList(ul);
            if (!CollectionUtils.isEmpty(userLikeList)) {
                for (UserLike userLike : userLikeList) {
                    userHasLikeMap.put(userLike.getPromptId() + userLike.getFileName(), userLike.getLoginName());
                }
            }
        }

        List<HomeFileResult> homeFileResults = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            for (PromptFilePageResult filePageResult : records) {
                HomeFileResult homeFileResult = new HomeFileResult();
                GenGenericPara paras = JsonUtils.fromJsonNode(filePageResult.getGenInfo(), GenGenericPara.class);
                BeanUtils.copyProperties(filePageResult, homeFileResult);
                homeFileResult.setAvatar(filePageResult.getThumbnailAvatarUrl());
                homeFileResult.setUserName(filePageResult.getUserName());
                homeFileResult.setImgName(filePageResult.getFileName());
                homeFileResult.setImgUrl(filePageResult.getFileUrl());
                homeFileResult.setPromptId(filePageResult.getPromptId());
                homeFileResult.setRealHeight(filePageResult.getHeight());
                homeFileResult.setRealWidth(filePageResult.getWidth());
                homeFileResult.setCompetition(Boolean.FALSE);
                homeFileResult.setOriginCreate(filePageResult.getOriginCreate());
                homeFileResult.setCreateTimestamp(filePageResult.getCreateTime());

                //判断是否具有敏感信息,有则不返回图片地址
        /*  if (!StringUtil.isBlank(promptFile.getSensitiveMessage())) {
              homeFileResult.setImgUrl("");
              homeFileResult.setThumbnailUrl("");
          }*/
                //获取当前图片点赞数量
                homeFileResult.setLikeNums(filePageResult.getLikeNums());
                if (StringUtils.isNotBlank(userHasLikeMap.get(filePageResult.getPromptId() + filePageResult.getFileName())) || userLikeFlag) {
                    homeFileResult.setHasLike(Boolean.TRUE);
                } else {
                    homeFileResult.setHasLike(Boolean.FALSE);
                }

                homeFileResult.setGenInfo(paras);

                homeFileResults.add(homeFileResult);
            }
        }

        PromptPageInfo<HomeFileResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(homeFileResults);
        promptPageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        return promptPageInfo;
    }

    public RandomPageInfo<HomeFileResult> getRandomList(Integer pageNum, Integer pageSize, User user, Boolean notWeb) throws JsonProcessingException {
        List<HomeFileResult> homeFileResults = new ArrayList<>();
        List<Integer> randomPageNumsList = new ArrayList<>();
        // 检查是否为null
        if (pageNum == null) {
            Integer pageTotal = exploreFileMapper.getExploreFileListTotal();
            pageTotal = pageTotal > 600 ? 600 : pageTotal;
            randomPageNumsList = RandomUtils.calculateRandomPagesWithOutLast(pageTotal, pageSize);
            if (!CollectionUtils.isEmpty(randomPageNumsList)) {
                pageNum = randomPageNumsList.get(0);
            }
        }

        // 根据promptId进行分页查询
        Page<ExploreFileListResult> page = new Page<>(pageNum, pageSize);
        IPage<ExploreFileListResult> iPage = exploreFileMapper.getExploreFileList(page);
        List<ExploreFileListResult> records = iPage.getRecords();

        /**
         * 查询当前用户是否对图片进行了点赞
         */
        Map<String, String> userHasLikeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(records)) {
            List<String> promptIds = records.stream().map(ExploreFileListResult::getPromptId).collect(Collectors.toList());
            LambdaQueryWrapper<UserLike> ul = new LambdaQueryWrapper<>();
            ul.eq(UserLike::getLoginName, user.getLoginName());
            ul.in(UserLike::getPromptId, promptIds);

            List<UserLike> userLikeList = userLikeMapper.selectList(ul);
            if (!CollectionUtils.isEmpty(userLikeList)) {
                for (UserLike userLike : userLikeList) {
                    userHasLikeMap.put(userLike.getPromptId() + userLike.getFileName(), userLike.getLoginName());
                }
            }
        }

        if (!CollectionUtils.isEmpty(records)) {
            for (ExploreFileListResult exploreFileListResult : records) {
                HomeFileResult homeFileResult = new HomeFileResult();
                GenGenericPara paras = JsonUtils.fromJsonNode(exploreFileListResult.getGenInfo(), GenGenericPara.class);
                BeanUtils.copyProperties(exploreFileListResult, homeFileResult);
                homeFileResult.setAvatar(exploreFileListResult.getThumbnailAvatarUrl());
                homeFileResult.setUserName(exploreFileListResult.getUserName());
                homeFileResult.setImgName(exploreFileListResult.getFileName());
                homeFileResult.setImgUrl(exploreFileListResult.getFileUrl());
                homeFileResult.setPromptId(exploreFileListResult.getPromptId());
                homeFileResult.setRealHeight(exploreFileListResult.getHeight());
                homeFileResult.setRealWidth(exploreFileListResult.getWidth());
                homeFileResult.setCompetition(Boolean.FALSE);
                homeFileResult.setOriginCreate(exploreFileListResult.getOriginCreate());
                homeFileResult.setCreateTimestamp(exploreFileListResult.getCreateTime());

                //获取当前图片点赞数量
                homeFileResult.setLikeNums(exploreFileListResult.getLikeNums());
                if (StringUtils.isNotBlank(userHasLikeMap.get(exploreFileListResult.getPromptId() + exploreFileListResult.getFileName()))) {
                    homeFileResult.setHasLike(Boolean.TRUE);
                } else {
                    homeFileResult.setHasLike(Boolean.FALSE);
                }

                homeFileResult.setGenInfo(paras);

                homeFileResults.add(homeFileResult);
            }
        }

        //将数据随机打乱
        SecureRandom secureRandom = new SecureRandom();
        Collections.shuffle(homeFileResults, secureRandom);

        RandomPageInfo<HomeFileResult> randomPageInfo = new RandomPageInfo<>();
        randomPageInfo.setResultList(homeFileResults);
        randomPageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        randomPageInfo.setPageNum(pageNum);
        randomPageInfo.setPageSize(pageSize);
        randomPageInfo.setRandomPageList(randomPageNumsList);
        return randomPageInfo;
    }

    public FileDetailResult getImgDetail(String imgName, String promptId, String loginName, Boolean notWeb) throws IOException {
        FileDetailResult detailResult = new FileDetailResult();

        // 根据promptId和文件名称查询对应信息
        LambdaQueryWrapper<PromptFile> qw = new LambdaQueryWrapper<>();
        qw.eq(PromptFile::getPromptId, promptId);
        qw.eq(PromptFile::getFileName, imgName);
        if (StringUtil.isNotBlank(loginName)) {
            qw.eq(PromptFile::getLoginName, loginName);
        }
        qw.eq(PromptFile::getDel, Boolean.FALSE);

        PromptFile promptFile = promptFileMapper.selectOne(qw);

        // 根据promptId查询对应的任务信息
        LambdaQueryWrapper<PromptRecord> prq = new LambdaQueryWrapper<>();
        prq.eq(PromptRecord::getPromptId, promptId);
        if (StringUtil.isNotBlank(loginName)) {
            prq.eq(PromptRecord::getLoginName, loginName);
        }
        prq.eq(PromptRecord::getDel, Boolean.FALSE);

        PromptRecord promptRecord = promptRecordMapper.selectOne(prq);

        if (!Objects.isNull(promptFile) && !Objects.isNull(promptRecord)) {
            // 查询用户的详情信息
            LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
            lqw.eq(User::getLoginName, promptRecord.getLoginName());
            User user = userMapper.selectOne(lqw);

            //查询模型相关的信息
            Map<String, ModelInformation.ModelAbout> modelAboutMap = new HashMap<>();
            List<ModelInformation.ModelAbout> modelAboutList = loadBalanceService.listModels();
            if (!CollectionUtils.isEmpty(modelAboutList)) {
                modelAboutMap = modelAboutList.stream().collect(Collectors.toMap(ModelInformation.ModelAbout::getModelId, Function.identity()));
            }
            GenGenericPara paras = JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class);
            ModelInformation.ModelAbout modelAbout = modelAboutMap.get(paras.getModel_id());

            BeanUtils.copyProperties(promptFile, detailResult);
            detailResult.setLoginName(promptRecord.getLoginName());
            if (!Objects.isNull(user)) {
                detailResult.setUserName(user.getUserName());
                detailResult.setAvatar(user.getThumbnailAvatarUrl());
            }
            if (!Objects.isNull(modelAbout)) {
                detailResult.setModelAvatar(modelAbout.getModelAvatar());
                detailResult.setModelDisplay(modelAbout.getModelDisplay());
            }
            detailResult.setImgName(promptFile.getFileName());
            detailResult.setImgUrl(promptFile.getFileUrl());
            detailResult.setThumbnailName(promptFile.getThumbnailName());
            detailResult.setThumbnailUrl(promptFile.getThumbnailUrl());
            detailResult.setPromptId(promptRecord.getPromptId());
            detailResult.setCreateTimestamp(promptFile.getCreateTime());
            detailResult.setGenInfo(paras);
            detailResult.setRealWidth(promptFile.getWidth());
            detailResult.setRealHeight(promptFile.getHeight());
            detailResult.setOriginCreate(promptRecord.getOriginCreate());
            detailResult.setSensitiveMessage(promptFile.getSensitiveMessage());

            List<String> tagList = new ArrayList<>();
            if (OriginCreate.picCreate.getValue().equals(promptRecord.getOriginCreate())) {
                for (GenGenericPara.StyleImg styleImg : paras.getMulti_img2img_info().getStyle_list()) {
                    tagList.add(Img2imgStyle.getLabelByValue(styleImg.getStyle()));
                }
            }
            if (OriginCreate.localRedraw.getValue().equals(promptRecord.getOriginCreate())) {
                tagList.add("Inpaint");
            }
            detailResult.setTagList(tagList);
        }

        return detailResult;
    }

    public UserLike getUserLike(String imgName, String promptId, String loginName) {
        LambdaQueryWrapper<UserLike> prq = new LambdaQueryWrapper<>();
        prq.eq(UserLike::getFileName, imgName);
        prq.eq(UserLike::getPromptId, promptId);
        prq.eq(UserLike::getLoginName, loginName);

        return userLikeMapper.selectOne(prq);
    }


    public Boolean addLike(String imgName, String promptId, String loginName, User user) {
        //点赞操作
        try {
            UserLike userLike = new UserLike();
            userLike.setLoginName(user.getLoginName());
            userLike.setPromptId(promptId);
            userLike.setFileName(imgName);
            userLike.setCreateTime(LocalDateTime.now());
            userLike.setCreateBy(user.getLoginName());
            userLikeMapper.insert(userLike);

            //维护信息到展示表
            LambdaQueryWrapper<ExploreFile> qw = new LambdaQueryWrapper<>();
            qw.eq(ExploreFile::getPromptId, promptId);
            qw.eq(ExploreFile::getFileName, imgName);
            qw.eq(ExploreFile::getDel, Boolean.FALSE);
            ExploreFile exploreFile = exploreFileMapper.selectOne(qw);

            if (!Objects.isNull(exploreFile)) {
                LambdaUpdateWrapper<ExploreFile> uw = new LambdaUpdateWrapper<>();
                uw.eq(ExploreFile::getId, exploreFile.getId());
                uw.set(ExploreFile::getLikeNums, exploreFile.getLikeNums() + 1);
                exploreFileMapper.update(null, uw);
            }

            //维护信息到原始图片表
            LambdaQueryWrapper<PromptFile> pqw = new LambdaQueryWrapper<>();
            pqw.eq(PromptFile::getPromptId, promptId);
            pqw.eq(PromptFile::getFileName, imgName);
            if (StringUtil.isNotBlank(loginName)) {
                pqw.eq(PromptFile::getLoginName, loginName);
            }
            pqw.eq(PromptFile::getDel, Boolean.FALSE);
            PromptFile promptFile = promptFileMapper.selectOne(pqw);

            if (!Objects.isNull(promptFile)) {
                LambdaUpdateWrapper<PromptFile> puw = new LambdaUpdateWrapper<>();
                puw.eq(PromptFile::getId, promptFile.getId());
                if (StringUtil.isNotBlank(loginName)) {
                    puw.eq(PromptFile::getLoginName, loginName);
                }
                puw.set(PromptFile::getLikeNums, promptFile.getLikeNums() + 1);
                promptFileMapper.update(null, puw);
            }
        } catch (Exception e) {
            log.error("用户：{}，点赞图片操作失败", user.getLoginName());
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public Boolean reduceLike(UserLike userLike, String originLoginName, String loginName) {
        //取消点赞操作
        try {
            userLikeMapper.deleteById(userLike.getId());

            //维护信息到展示表
            LambdaQueryWrapper<ExploreFile> qw = new LambdaQueryWrapper<>();
            qw.eq(ExploreFile::getPromptId, userLike.getPromptId());
            qw.eq(ExploreFile::getFileName, userLike.getFileName());
            qw.eq(ExploreFile::getDel, Boolean.FALSE);
            ExploreFile exploreFile = exploreFileMapper.selectOne(qw);

            if (!Objects.isNull(exploreFile) && exploreFile.getLikeNums() > 0) {
                LambdaUpdateWrapper<ExploreFile> uw = new LambdaUpdateWrapper<>();
                uw.eq(ExploreFile::getId, exploreFile.getId());
                uw.set(ExploreFile::getLikeNums, exploreFile.getLikeNums() - 1);
                exploreFileMapper.update(null, uw);
            }

            //维护信息到原始图片表
            LambdaQueryWrapper<PromptFile> pqw = new LambdaQueryWrapper<>();
            pqw.eq(PromptFile::getPromptId, userLike.getPromptId());
            pqw.eq(PromptFile::getFileName, userLike.getFileName());
            if (StringUtil.isNotBlank(originLoginName)) {
                pqw.eq(PromptFile::getLoginName, originLoginName);
            }
            pqw.eq(PromptFile::getDel, Boolean.FALSE);
            PromptFile promptFile = promptFileMapper.selectOne(pqw);

            if (!Objects.isNull(promptFile) && promptFile.getLikeNums() > 0) {
                LambdaUpdateWrapper<PromptFile> puw = new LambdaUpdateWrapper<>();
                puw.eq(PromptFile::getId, promptFile.getId());
                if (StringUtil.isNotBlank(originLoginName)) {
                    puw.eq(PromptFile::getLoginName, originLoginName);
                }
                puw.set(PromptFile::getLikeNums, promptFile.getLikeNums() - 1);
                promptFileMapper.update(null, puw);
            }
        } catch (Exception e) {
            log.error("用户：{}，取消点赞图片操作失败", loginName);
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }


    public Boolean deleteByList(List<ImgDelete> imgDeleteList, User user) {
        //大于40张图片，分批操作
        List<List<ImgDelete>> partition = Lists.partition(imgDeleteList, 20);
        for (List<ImgDelete> list : partition) {
            deleteByImgDeleteList(list, user);
        }
        return Boolean.TRUE;
    }

    /**
     * 批量处理20条，异常进行回滚
     *
     * @param imgDeleteList
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByImgDeleteList(List<ImgDelete> imgDeleteList, User user) {
        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());
        try {
            log.info("用户：{}，删除图片操作开始", user.getLoginName());
            List<String> promptIds = new ArrayList<>();
            List<String> imgNames = new ArrayList<>();

            for (ImgDelete imgDelete : imgDeleteList) {
                promptIds.add(imgDelete.getPromptId());
                imgNames.add(imgDelete.getImgName());
            }

            // 逻辑删除
            LambdaUpdateWrapper<PromptFile> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(PromptFile::getDel, Boolean.TRUE)
                    .set(PromptFile::getUpdateTime, LocalDateTime.now())
                    .set(PromptFile::getUpdateBy, user.getLoginName())
                    .in(PromptFile::getPromptId, promptIds)
                    .in(PromptFile::getFileName, imgNames)
                    .eq(PromptFile::getLoginName, user.getLoginName());

            promptFileMapper.update(null, updateWrapper);

            // 对任务进行判断，如果已经没有图片了，则逻辑删除任务
            List<String> emptyPromptRecordPromptIds = promptRecordMapper.getPromptRecordEmptyImg(promptIds, user.getLoginName());
            if (!CollectionUtils.isEmpty(emptyPromptRecordPromptIds)) {
                promptRecordMapper.updateDelFlagForUnusedPrompts(emptyPromptRecordPromptIds, user.getLoginName());
            }

            // 对所有被收藏的信息进行逻辑删除
            LambdaQueryWrapper<UserCollect> collectQueryWrapper = new LambdaQueryWrapper<>();
            collectQueryWrapper.in(UserCollect::getPromptId, promptIds)
                    .in(UserCollect::getFileName, imgNames)
                    .eq(UserCollect::getLoginName, user.getLoginName());
            List<UserCollect> userCollectList = userCollectMapper.selectList(collectQueryWrapper);

            List<String> noCollectImgNameList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(userCollectList)) {
                List<String> collectImgNameList = userCollectList.stream().map(UserCollect::getFileName).collect(Collectors.toList());
                //取出没有被收藏的图片名称
                noCollectImgNameList = imgNames.stream().filter(imgName -> !collectImgNameList.contains(imgName)).collect(Collectors.toList());
            }

            //删除对应的审核记录
            if (!CollectionUtils.isEmpty(noCollectImgNameList)) {
                LambdaQueryWrapper<PublicFileReview> lqw = new LambdaQueryWrapper<>();
                lqw.in(PublicFileReview::getPromptId, promptIds);
                lqw.in(PublicFileReview::getFileName, noCollectImgNameList);
                lqw.eq(PublicFileReview::getLoginName, user.getLoginName());
                publicFileReviewMapper.delete(lqw);
            }

            log.info("用户：{}，删除图片操作完成", user.getLoginName());
            List<PromptFile> promptFiles = promptFileService.lambdaQuery()
                    .in(PromptFile::getPromptId, promptIds)
                    .in(PromptFile::getFileName, imgNames)
                    .eq(PromptFile::getDel, Boolean.TRUE)
                    .eq(PromptFile::getLoginName, user.getLoginName())
                    .eq(PromptFile::getCollectNums, 0)
                    .list();
            if (CollUtil.isNotEmpty(promptFiles)) {
                List<String> urlList = addImgUrl(promptFiles);
                promptFileService.lambdaUpdate()
                        .in(PromptFile::getId, promptFiles.stream().map(PromptFile::getId).collect(Collectors.toList()))
                        .eq(PromptFile::getLoginName, user.getLoginName())
                        .remove();
                imgUploadCommonService.deleteObjectsWithUrlListAsync(urlList);
            }
            return Boolean.TRUE;
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }


    public StatisticsImgNums getStatisticsUserImg(User user) {
        StatisticsImgNums statisticsImgNums = new StatisticsImgNums();
        //获取用户当天生图数量
        Integer userCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, user.getLoginName());
        userCreateImgNums = !Objects.isNull(userCreateImgNums) ? userCreateImgNums : 0;
        statisticsImgNums.setTodayCreateImgNums(userCreateImgNums);
        //获取用户总的生图数量
        Integer userAllCreateImgNums = !Objects.isNull(user.getTotalImgNum()) ? user.getTotalImgNum() : 0;
        statisticsImgNums.setAllCreateImgNums(userAllCreateImgNums);

        //每日免费点数
        statisticsImgNums.setDailyLumens(user.getDailyLumens());
        //用户使用点数
        statisticsImgNums.setUseDailyLumens(user.getUseDailyLumens());

        return statisticsImgNums;
    }

    public PromptPageInfo<HomeFileResult> getCommonExploreList(Integer pageNum, Integer pageSize, String collationName, Boolean notWeb) throws JsonProcessingException {
        List<String> opexLoginNameList = new ArrayList<>();
        if (StringUtil.isNotBlank(opexLoginName)) {
            opexLoginNameList = Arrays.stream(opexLoginName.split(",")).collect(Collectors.toList());
        }
        List<HomeFileResult> homeFileResults = new ArrayList<>();
        // 根据promptId进行分页查询
        Page<PromptFilePageResult> page = new Page<>(pageNum, pageSize);
        IPage<PromptFilePageResult> iPage = promptFileMapper.getPromptFileList(page, "", "", collationName, opexLoginNameList);
        List<PromptFilePageResult> records = iPage.getRecords();

        if (!CollectionUtils.isEmpty(records)) {
            for (PromptFilePageResult promptFilePageResult : records) {
                HomeFileResult homeFileResult = new HomeFileResult();
                GenGenericPara paras = JsonUtils.fromJsonNode(promptFilePageResult.getGenInfo(), GenGenericPara.class);
                BeanUtils.copyProperties(promptFilePageResult, homeFileResult);
                homeFileResult.setAvatar(promptFilePageResult.getThumbnailAvatarUrl());
                homeFileResult.setUserName(promptFilePageResult.getUserName());
                homeFileResult.setImgName(promptFilePageResult.getFileName());
                homeFileResult.setImgUrl(promptFilePageResult.getFileUrl());
                homeFileResult.setPromptId(promptFilePageResult.getPromptId());
                homeFileResult.setRealHeight(promptFilePageResult.getHeight());
                homeFileResult.setRealWidth(promptFilePageResult.getWidth());
                homeFileResult.setOriginCreate(promptFilePageResult.getOriginCreate());
                homeFileResult.setCreateTimestamp(promptFilePageResult.getCreateTime());
                //获取当前图片点赞数量
                homeFileResult.setLikeNums(promptFilePageResult.getLikeNums());
                homeFileResult.setGenInfo(paras);

                homeFileResults.add(homeFileResult);
            }
        }

        PromptPageInfo<HomeFileResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(homeFileResults);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        return promptPageInfo;
    }

    public Boolean addReport(UserAddReportDto userAddReportDto, User user) {

        // 复制 DTO 数据并保存举报信息
        UserReport userReport = new UserReport();
        BeanUtils.copyProperties(userAddReportDto, userReport);
        userReport.setCreateTime(LocalDateTime.now());
        userReport.setCreateBy(user.getId().toString());
        userReport.setLoginName(user.getLoginName());
        userReport.setFileName(userAddReportDto.getImgName());

        // 插入举报记录
        userReportMapper.insert(userReport);

        return Boolean.TRUE;
    }

    public Boolean publicUserImg(String imgName, String promptId, String brief, String publicType, User user,String activityId) {

        //判断图片是否存在
        LambdaQueryWrapper<PromptFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PromptFile::getFileName, imgName);
//        queryWrapper.eq(PromptFile::getDel, Boolean.FALSE);
        queryWrapper.eq(PromptFile::getPromptId, promptId);
        queryWrapper.eq(PromptFile::getLoginName, user.getLoginName());
        Long aLong = promptFileMapper.selectCount(queryWrapper);

        if (aLong < 1) {
            throw new ServerInternalException("The picture does not exist or has been deleted");
        }

        //获取图片信息
        PromptFile promptFile = promptFileMapper.selectOne(queryWrapper);

        if (StringUtil.isNotBlank(promptFile.getSensitiveMessage())) {
            throw new ServerInternalException("Please do not post NSFW pictures");
        }

        if (!promptFile.getIsPublic().equals(PublicType.undisclosed.getValue())) {
            throw new ServerInternalException("Please do not resubmit");
        }

        if (OriginCreate.customUpload.getValue().equals(promptFile.getOriginCreate())) {
            throw new ServerInternalException("User pictures are not supported");
        }
        PublicFileReview publicFileReview = new PublicFileReview();

        publicFileReview.setFileUrl(promptFile.getFileUrl());
        publicFileReview.setMiniThumbnailUrl(promptFile.getMiniThumbnailUrl());
        publicFileReview.setHighMiniUrl(promptFile.getHighMiniUrl());
        publicFileReview.setThumbnailUrl(promptFile.getThumbnailUrl());
        publicFileReview.setHighThumbnailUrl(promptFile.getHighThumbnailUrl());
        publicFileReview.setFileName(imgName);
        publicFileReview.setPromptId(promptId);
        publicFileReview.setLoginName(user.getLoginName());
        publicFileReview.setReviewStatus("review");
        publicFileReview.setPublicType(publicType);
        publicFileReview.setBrief(brief);
        publicFileReview.setRealHeight(promptFile.getHeight());
        publicFileReview.setRealWidth(promptFile.getWidth());
        publicFileReview.setCreateBy(user.getId().toString());
        publicFileReview.setCreateTime(LocalDateTime.now());
        publicFileReview.setFileId(promptFile.getId().toString());
        publicFileReview.setFileType(Objects.nonNull(promptFile.getDuration()) && promptFile.getDuration() > 0 ?
                FileTypeEnum.VIDEO.getCode() : FileTypeEnum.IMAGE.getCode());
        publicFileReview.setVideoUrl(promptFile.getVideoUrl());
        if (StringUtils.isNotBlank(activityId)){
            CommActivity commActivity = validateActivity(activityId);
            // 活动类型：0-图片 1-视频 2-all
            Integer activityType = commActivity.getType();
            Integer duration = promptFile.getDuration();
            boolean isVideo = duration != null && duration > 0;
            if (activityType != null) {
                if (FileTypeEnum.IMAGE.getCode().equals(activityType) && isVideo) {
                    // 图片活动禁止视频投稿
                    throw new LogicException(LogicErrorCode.NOT_MEET_ACTIVITY_CONDITIONS);
                } else if ( FileTypeEnum.VIDEO.getCode().equals(activityType) && !isVideo) {
                    // 视频活动禁止图片投稿
                    throw new LogicException(LogicErrorCode.NOT_MEET_ACTIVITY_CONDITIONS);
                }
                // activityType == 2 (all) 不做限制
            }
            checkSubmissionLimit(commActivity.getId(),user.getLoginName(),1,commActivity.getMaxSubmissions(),user.getId());
            publicFileReview.setActivityId(activityId);
        }

        // 查询生图记录
        LambdaQueryWrapper<PromptRecord> ruw = new LambdaQueryWrapper<>();
        ruw.eq(PromptRecord::getPromptId, promptId);
        ruw.eq(PromptRecord::getLoginName, user.getLoginName());
        PromptRecord promptRecord = promptRecordMapper.selectOne(ruw);

        if (Objects.isNull(promptRecord)) {
            throw new ServerInternalException("The picture does not exist or has been deleted");
        }

        publicFileReview.setGenInfo(promptRecord.getGenInfo());
        publicFileReview.setPrompt(promptRecord.getPrompt());

        try {
            //查询模型相关的信息
            Map<String, ModelInformation.ModelAbout> modelAboutMap = new HashMap<>();
            List<ModelInformation.ModelAbout> modelAboutList = loadBalanceService.listModels();
            if (!CollectionUtils.isEmpty(modelAboutList)) {
                modelAboutMap = modelAboutList.stream().collect(Collectors.toMap(ModelInformation.ModelAbout::getModelId, Function.identity()));
            }
            ModelInformation.ModelAbout modelAbout = modelAboutMap.get(promptRecord.getModelId());
            if (!Objects.isNull(modelAbout)) {
                publicFileReview.setModelDisplay(modelAbout.getModelDisplay());
            }
        } catch (IOException e) {
            log.info("获取模型信息失败{}",e);
            publicFileReview.setModelDisplay("");
        }

        publicFileReviewMapper.insert(publicFileReview);

        // 更改状态为审核中
        LambdaUpdateWrapper<PromptFile> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PromptFile::getLoginName, user.getLoginName());
        updateWrapper.eq(PromptFile::getId, promptFile.getId());
        updateWrapper.set(PromptFile::getIsPublic, PublicType.review.getValue());

        promptFileMapper.update(null, updateWrapper);

        //记录用户当天发布到社区图片数
        redisService.incrementHashValue(USER_PUBLIC_IMG_NUMS, user.getLoginName(), 1);

        return Boolean.TRUE;
    }

    public Integer publicUserImgSurplusNums(User user) {
        Integer todayPublicImgNums = 50 - getTodayPublicImgNums(user.getLoginName());
        return todayPublicImgNums > 0 ? todayPublicImgNums : 0;
    }

    public int getTodayPublicImgNums(String loginName) {
        Object todayPublicImgNums = redisService.getDataFromHash(USER_PUBLIC_IMG_NUMS, loginName);
        if (!Objects.isNull(todayPublicImgNums)) {
            return (int) todayPublicImgNums;
        } else {
            return 0;
        }
    }


    public void initWebp90() {
        LambdaQueryWrapper<ExploreFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(ExploreFile::getHighThumbnailUrl);
        queryWrapper.isNotNull(ExploreFile::getFileUrl);
        queryWrapper.eq(ExploreFile::getDel, Boolean.FALSE);
        List<ExploreFile> exploreFiles = exploreFileMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(exploreFiles)) {
            return;
        }
        for (ExploreFile exploreFile : exploreFiles) {
            String fileUrl = exploreFile.getFileUrl();
            String loginName = exploreFile.getLoginName();
            String userId = userService.queryUserIdByName(loginName);
            String highThumbnailName = buildFileNameKey(userId, "webp");
            boolean isOld = false;
            if (fileUrl.contains("piclumen-1324066212")) {
                isOld = true;
            }
            try {
                imgUploadCommonService.imageProcessToWebp90(URLUtil.getPath(fileUrl), highThumbnailName, isOld);
            } catch (Exception e) {
                log.error("imageProcessToWebp90 error", e);
                continue;
            }
            exploreFile.setHighThumbnailName(highThumbnailName);
            exploreFile.setHighThumbnailUrl(cosConfig.getCosAccelerateDomain() + highThumbnailName);
            if (highThumbnailName.startsWith("/")) {
                exploreFile.setHighThumbnailName(highThumbnailName.substring(1));
            }
            exploreFileMapper.updateById(exploreFile);
        }
    }


    public PromptFileRecord getGenPromptFileRecord(String promptId, User user) throws JsonProcessingException {
        PromptFileRecord promptFileRecord = new PromptFileRecord();
        //模糊分页查询任务
        LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<>();
        qw.eq(PromptRecord::getDel, Boolean.FALSE);
        qw.eq(PromptRecord::getLoginName, user.getLoginName());
        qw.eq(PromptRecord::getPromptId, promptId);

        PromptRecord promptRecord = promptRecordMapper.selectOne(qw);

        //对图片数据进行封装
        if (!Objects.isNull(promptRecord)) {
            LambdaQueryWrapper<PromptFile> prq = new LambdaQueryWrapper<>();
            prq.eq(PromptFile::getPromptId, promptId);
            prq.eq(PromptFile::getLoginName, user.getLoginName());
            prq.eq(PromptFile::getDel, Boolean.FALSE);

            List<PromptFile> promptFileList = promptFileMapper.selectList(prq);

            if (!CollectionUtils.isEmpty(promptFileList)) {
                BeanUtils.copyProperties(promptRecord, promptFileRecord, "genInfo");
                promptFileRecord.setGenInfo(JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class));
                promptFileRecord.setAvatar(user.getThumbnailAvatarUrl());
                promptFileRecord.setUserName(user.getUserName());
                promptFileRecord.setCreateTimestamp(promptRecord.getCreateTime());

                List<PromptFileRecord.ImgUrl> imgUrlList = new ArrayList<>();
                for (PromptFile promptFile : promptFileList) {
                    //对图片数据进行转换
                    PromptFileRecord.ImgUrl imgUrl = new PromptFileRecord.ImgUrl();
                    imgUrl.setId(promptFile.getId());
                    imgUrl.setImgName(promptFile.getFileName());
                    imgUrl.setImgUrl(promptFile.getFileUrl());
                    imgUrl.setThumbnailName(promptFile.getThumbnailName());
                    imgUrl.setThumbnailUrl(promptFile.getThumbnailUrl());
                    imgUrl.setHighThumbnailName(promptFile.getHighThumbnailName());
                    imgUrl.setHighThumbnailUrl(promptFile.getHighThumbnailUrl());
                    imgUrl.setMiniThumbnailUrl(promptFile.getMiniThumbnailUrl());
                    imgUrl.setHighMiniUrl(promptFile.getHighMiniUrl());
                    imgUrl.setSensitive(promptFile.getSensitiveMessage());
                    imgUrl.setRealWidth(promptFile.getWidth());
                    imgUrl.setRealHeight(promptFile.getHeight());
                    imgUrl.setOriginCreate(promptRecord.getOriginCreate());
                    imgUrl.setIsPublic(promptFile.getIsPublic());
                    imgUrl.setRejectionContent(promptFile.getRejectionContent());
                    imgUrlList.add(imgUrl);
                }

                //将图片封装到任务中
                promptFileRecord.setImg_urls(imgUrlList);
            }
        }
        return promptFileRecord;
    }

    public Boolean publicUserActivityImg(CommActivityPostsDto dto, User user) {
        String activityId = dto.getActivityId();
        String loginName = user.getLoginName();

        // 校验活动是否存在且为进行中
        CommActivity commActivity = validateActivity(activityId);

        // 收集投稿信息
        Map<String, String> imgNameMap = new HashMap<>();
        Set<String> promptIds = new HashSet<>();
        Set<String> imgNames = new HashSet<>();

        for (ActivityPublish publish : dto.getActivityPublishList()) {
            promptIds.add(publish.getPromptId());
            imgNames.add(publish.getImgName());
            imgNameMap.put(publish.getImgName(), publish.getPublicType());
        }

        // 查询符合条件的 PromptFile
        List<PromptFile> promptFileList = getPromptFiles(promptIds, imgNames, loginName);
        // 活动类型过滤：0-图片 1-视频 2-all
        Integer activityType = commActivity.getType();
        if (activityType != null) {
            if (FileTypeEnum.IMAGE.getCode().equals(activityType)) {
                // 图片活动：过滤掉视频（duration 不为空且 > 0）
                promptFileList.removeIf(f -> f.getDuration() != null && f.getDuration() > 0);
            } else if (FileTypeEnum.VIDEO.getCode().equals(activityType)) {
                // 视频活动：过滤掉图片（duration 为空或 <= 0）
                promptFileList.removeIf(f -> f.getDuration() == null || f.getDuration() <= 0);
            }
            // activityType == 2（all）不做过滤
        }
        if (CollectionUtils.isEmpty(promptFileList)) {
            throw new LogicException(LogicErrorCode.USER_PUBLIC_RESUBMIT);
        }

        // 过滤后的 id 集合
        Set<String> filteredPromptIds = promptFileList.stream()
                .map(PromptFile::getPromptId)
                .collect(Collectors.toSet());
        Set<String> filteredImgNames = promptFileList.stream()
                .map(PromptFile::getFileName)
                .collect(Collectors.toSet());

        // 判断是否超投稿限制（基于过滤后的数量）
        checkSubmissionLimit(Long.parseLong(activityId), loginName, promptFileList.size(), commActivity.getMaxSubmissions(),user.getId());

        // 查询相关 prompt record（基于过滤后的 promptIds）
        Map<String, PromptRecord> promptRecordMap = getPromptRecordMap(filteredPromptIds, loginName);

        Map<String, ModelInformation.ModelAbout> modelAboutMap = new HashMap<>();
        try {
            List<ModelInformation.ModelAbout> modelAboutList = loadBalanceService.listModels();

            //查询模型相关的信息
             if (!CollectionUtils.isEmpty(modelAboutList)) {
                modelAboutMap = modelAboutList.stream().collect(Collectors.toMap(ModelInformation.ModelAbout::getModelId, Function.identity()));
            }
        } catch (IOException e) {
            log.error("获取模型列表失败{}", e);
        }

        Map<String, ModelInformation.ModelAbout> finalModelAboutMap = modelAboutMap;
        List<PublicFileReview> reviewList = promptFileList.stream()
                .map(file -> {
                    PublicFileReview publicFileReview = new PublicFileReview();
                    publicFileReview.setFileUrl(file.getFileUrl());
                    publicFileReview.setMiniThumbnailUrl(file.getMiniThumbnailUrl());
                    publicFileReview.setHighMiniUrl(file.getHighMiniUrl());
                    publicFileReview.setThumbnailUrl(file.getThumbnailUrl());
                    publicFileReview.setHighThumbnailUrl(file.getHighThumbnailUrl());
                    publicFileReview.setFileName(file.getFileName());
                    publicFileReview.setPromptId(file.getPromptId());
                    publicFileReview.setLoginName(user.getLoginName());
                    publicFileReview.setReviewStatus("review");
                    publicFileReview.setPublicType(imgNameMap.get(file.getFileName()));
                    publicFileReview.setRealHeight(file.getHeight());
                    publicFileReview.setRealWidth(file.getWidth());
                    publicFileReview.setCreateBy(user.getId().toString());
                    publicFileReview.setCreateTime(LocalDateTime.now());
                    publicFileReview.setFileId(file.getId().toString());
                    publicFileReview.setActivityId(activityId);
                    PromptRecord promptRecord = promptRecordMap.get(file.getPromptId());

                    if (promptRecord != null) {
                        ModelInformation.ModelAbout modelAbout = finalModelAboutMap.get(promptRecord.getModelId());
                        if (!Objects.isNull(modelAbout)) {
                            publicFileReview.setModelDisplay(modelAbout.getModelDisplay());
                        }
                        publicFileReview.setGenInfo(promptRecord.getGenInfo());
                        publicFileReview.setPrompt(promptRecord.getPrompt());
                    }

                    publicFileReview.setFileType(Objects.nonNull(file.getDuration()) && file.getDuration() > 0 ?
                            FileTypeEnum.VIDEO.getCode() : FileTypeEnum.IMAGE.getCode());
                    publicFileReview.setVideoUrl(file.getVideoUrl());

                    return publicFileReview;
                })
                .collect(Collectors.toList());

        // 增加用户今日投稿数
        redisService.incrementHashValue(USER_PUBLIC_IMG_NUMS, loginName, promptFileList.size());

        // 实际插入数据库的代码应在此处（示例中省略）
        publicFileReviewService.saveBatch(reviewList);

        // 更改状态为审核中
        LambdaUpdateWrapper<PromptFile> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PromptFile::getLoginName, user.getLoginName());
        updateWrapper.in(PromptFile::getPromptId, filteredPromptIds);
        updateWrapper.in(PromptFile::getFileName, filteredImgNames);
        updateWrapper.set(PromptFile::getIsPublic, PublicType.review.getValue());

        promptFileMapper.update(null, updateWrapper);

        return Boolean.TRUE;
    }

    private CommActivity validateActivity(String activityId) {
        LambdaQueryWrapper<CommActivity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommActivity::getId, activityId)
                .eq(CommActivity::getPublish, true)
                .le(CommActivity::getBeginTime, LocalDateTime.now())
                .ge(CommActivity::getPostEndTime, LocalDateTime.now());
        CommActivity activity = commActivityMapper.selectOne(wrapper);
        if (activity == null) {
            throw new LogicException(LogicErrorCode.USER_ACTIVITY_TIME_PASSED);
        }
        return activity;
    }

    private List<PromptFile> getPromptFiles(Set<String> promptIds, Set<String> imgNames, String loginName) {
        LambdaQueryWrapper<PromptFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PromptFile::getPromptId, promptIds)
                .in(PromptFile::getFileName, imgNames)
                .eq(PromptFile::getLoginName, loginName)
                .eq(PromptFile::getIsPublic, PublicType.undisclosed);
        return promptFileMapper.selectList(wrapper);
    }

    private void checkSubmissionLimit(Long activityId, String loginName, int newSubmissionCount, Integer maxSubmissions,Long userId) {
        // MySQL 投稿记录数
        LambdaQueryWrapper<PublicFileReview> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PublicFileReview::getLoginName, loginName)
                .eq(PublicFileReview::getActivityId, activityId);
        long existingReviewCount = publicFileReviewMapper.selectCount(wrapper);

        // Mongo 投稿记录数
        Query query = new Query();
        query.addCriteria(Criteria.where("accountInfo.userId").is(userId))
                .addCriteria(Criteria.where("activityId").is(activityId));
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

        if (existingReviewCount + commFileList.size() + newSubmissionCount > maxSubmissions) {
            log.info("用户：{},活动id：{},超过此活动限制：{}张",loginName,activityId,maxSubmissions);
            throw new LogicException(LogicErrorCode.USER_PUBLIC_ACTIVITY_LIMIT);
        }
    }

    private Map<String, PromptRecord> getPromptRecordMap(Set<String> promptIds, String loginName) {
        LambdaQueryWrapper<PromptRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PromptRecord::getPromptId, promptIds)
                .eq(PromptRecord::getLoginName, loginName)
                .select(PromptRecord::getPromptId, PromptRecord::getGenInfo, PromptRecord::getPrompt,PromptRecord::getModelId);
        List<PromptRecord> records = promptRecordMapper.selectList(wrapper);
        return records.stream().collect(Collectors.toMap(PromptRecord::getPromptId, Function.identity()));
    }
}
