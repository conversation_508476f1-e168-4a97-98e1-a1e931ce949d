package com.lx.pl.service.resources;

import com.lx.pl.constant.LogicConstants;
import com.lx.pl.service.RedisService;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description
 */
@Slf4j
@Service
public class CommonResourcesService {
    @Resource
    private RedisService redisService;

    public List<String> availablePaymentChannels(String platform) {
        try {
            String key = LogicConstants.AVAILABLE_PAYMENT_CHANNELS_KEY_PREFIX + platform.toLowerCase();
            Object data = redisService.get(key);
            if (Objects.isNull(data)) {
                return Collections.emptyList();
            }
            return JsonUtils.writeToList((String) data, String.class);
        } catch (Exception e) {
            log.error("从缓存中获取可用的支付渠道异常, platform: {}", platform, e);
            return Collections.emptyList();
        }
    }
}
