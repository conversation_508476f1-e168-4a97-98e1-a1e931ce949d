package com.lx.pl.service;

import com.lx.pl.db.mysql.community.entity.LumenChangeRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description lumen变化记录Service
 */
@Slf4j
@Service
public class LumenChangeRecordService {
    @Resource
    private MongoTemplate mongoTemplate;

    public void saveOrUpdateLumenChangeRecord(User user,
                                              int changeLumen,
                                              String changeType,
                                              String source,
                                              String detail,
                                              String batchId) {
        saveOrUpdateRecord(user.getId(), user.getLoginName(), changeLumen, changeType, source, detail, batchId, null, null);
    }

    private void saveOrUpdateRecord(Long userId,
                                    String userLoginName, int changeLumen, String changeType, String source, String detail, String batchId, Long relationId, Long timestamp) {
        if (StringUtils.isNotBlank(batchId)) {
            LumenChangeRecord one = mongoTemplate.findOne(
                    new Query(Criteria.where("batchId").is(batchId)
                            .and("userId").is(userId)),
                    LumenChangeRecord.class
            );
            if (one != null) {
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("id").is(one.getId())),
                        new Update().inc("changeLumen", changeLumen)
                                .set("updateTime", LocalDateTime.now()), LumenChangeRecord.class
                );
                return;
            }
        }
        LumenChangeRecord changeRecord = new LumenChangeRecord();
        changeRecord.setUserId(userId);
        changeRecord.setUserLoginName(userLoginName);
        changeRecord.setChangeType(changeType);
        changeRecord.setChangeLumen(changeLumen);
        changeRecord.setSource(source);
        changeRecord.setDetail(detail);
        changeRecord.setBatchId(batchId);
        changeRecord.setRelationId(relationId);
        if (timestamp != null) {
            changeRecord.setHappenTimeSec(timestamp);
        } else {
            changeRecord.setHappenTimeSec(Instant.now().getEpochSecond());
        }
        changeRecord.setCreateTime(LocalDateTime.now());
        changeRecord.setUpdateTime(LocalDateTime.now());
        changeRecord.setCreateBy(detail);
        changeRecord.setUpdateBy(detail);
        mongoTemplate.insert(changeRecord);
    }

    public void saveOrUpdateLumenChangeRecord(Long userId,
                                              String userLoginName,
                                              int changeLumen,
                                              String changeType,
                                              String source,
                                              String detail,
                                              Long relationId,
                                              Long happenTime) {
        try {
            saveOrUpdateRecord(userId, userLoginName, changeLumen, changeType, source, detail, null, relationId, happenTime);
        } catch (Exception e) {
            log.error("保存lumen变化记录失败", e);
        }
    }

    public void removeLumenChangeRecordByRelationId(Long relationId) {
        try {
            mongoTemplate.remove(
                    new Query(Criteria.where("relationId").is(relationId)),
                    LumenChangeRecord.class
            );
        } catch (Exception e) {
            log.error("删除lumen变化记录失败", e);
        }
    }
}
