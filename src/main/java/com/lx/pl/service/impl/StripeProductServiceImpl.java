package com.lx.pl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.mapper.StripeProductMapper;
import com.lx.pl.enums.StripeErrorCode;
import com.lx.pl.exception.PaymentException;
import com.lx.pl.pay.paypal.model.vo.ProductItem;
import com.lx.pl.pay.stripe.dto.PaymentType;
import com.lx.pl.service.StripeProductService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StripeProductServiceImpl extends ServiceImpl<StripeProductMapper, StripeProduct> implements StripeProductService {
    private static final Logger logger = LoggerFactory.getLogger(StripeProductServiceImpl.class);

    // 根据 Stripe 生成的产品 ID 获取 StripeProduct 对象
    @Override
    public StripeProduct getStripeProductByStripeProductId(String stripeProductId) {
        logger.info("Fetching StripeProduct by Stripe Product ID: {}", stripeProductId);
        LambdaQueryWrapper<StripeProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StripeProduct::getStripeProductId, stripeProductId);
        return this.getOne(queryWrapper);
    }

    // 根据 planLevel, productType, priceInterval 查询 StripeProduct 对象
    @Override
    public StripeProduct getStripeProductByLevelTypeInterval(String planLevel, String productType, String priceInterval) {
        logger.info("Fetching StripeProduct by planLevel: {}, productType: {}, priceInterval: {}", planLevel, productType, priceInterval);
        LambdaQueryWrapper<StripeProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StripeProduct::getPlanLevel, planLevel)
                .eq(StripeProduct::getProductType, productType)
                .eq(StripeProduct::getStatus, 1)
                .eq(StripeProduct::getPriceInterval, priceInterval);
        return this.getOne(queryWrapper);
    }

    // 根据 priceId 查询 StripeProduct 对象
    @Override
    public StripeProduct getStripeProductByPriceId(String priceId) {
        logger.info("Fetching StripeProduct by priceId: {}", priceId);
        LambdaQueryWrapper<StripeProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StripeProduct::getStripePriceId, priceId);
        return this.getOne(queryWrapper);
    }

    // 传入BuyItemDto对象，获取planLevel productType priceInterval 查询StripeProduct 对象
    @Override
    public StripeProduct getStripeProductByBuyItemDto(ProductItem stripeItem) {
        logger.debug("Fetching StripeProduct by StripeItem: {}", stripeItem);
        // 获取支付方式类型
        PaymentType paymentType = stripeItem.getType();
        // 使用switch语句处理不同的支付类型
        switch (paymentType) {
            case PLAN:
                // 处理订阅类型的产品
                StripeProduct stripeProductByLevelTypeInterval = getStripeProductByLevelTypeInterval(stripeItem.getProduct(), paymentType.getType(), stripeItem.getPrice());
                if (stripeProductByLevelTypeInterval == null) {
                    logger.error("StripeProduct not found for planLevel: {}, productType: {}, priceInterval: {}", stripeItem.getProduct(), paymentType.getType(), stripeItem.getPrice());
                    throw new PaymentException(StripeErrorCode.NO_STRIPE_PRODUCT_FOUND);
                }
                return stripeProductByLevelTypeInterval;
            case ONE:
                // 处理一次性支付类型的产品
                // 使用 LambdaQueryWrapper 明确构建查询条件
                LambdaQueryWrapper<StripeProduct> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(StripeProduct::getProductType, paymentType.getType());
                queryWrapper.eq(StripeProduct::getStatus, 1);
                // 使用 getOne 方法查询单个 StripeProduct 对象
                StripeProduct one = this.getOne(queryWrapper);
                if (one == null) {
                    logger.error("StripeProduct not found for productType: {}", paymentType.getType());
                    throw new PaymentException(StripeErrorCode.NO_STRIPE_PRODUCT_FOUND);
                }
                return one;
            default:
                logger.error("Unsupported payment type: {}", paymentType);
                throw new PaymentException(StripeErrorCode.UNSUPPORTED_PAYMENT_TYPE);
        }
    }

    @Override
    public List<StripeProduct> getOneTimeProductListByLumenQty(List<Integer> lumenQtyList) {
        logger.info("Fetching StripeProducts by lumenQty: {}", lumenQtyList);
        return this.lambdaQuery().in(StripeProduct::getLumen, lumenQtyList)
                .eq(StripeProduct::getProductType, PaymentType.ONE.getType())
                .eq(StripeProduct::getStatus, 1)
                .list();
    }

}
