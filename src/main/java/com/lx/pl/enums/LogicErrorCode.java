package com.lx.pl.enums;

/**
 * @Description: logic处理异常枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2024/12/27
 */
public enum LogicErrorCode {
    ILLEGAL_PROMPT("4001", "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.", "提示词涉及儿童色情词汇"),

    EXCEED_CONCURRENT_JOBS("4002", "Exceed  Concurrent Jobs ", "超过并发任务限制"),

    EXCEED_TASK_QUEUE("4003", "Exceed_TaskQueue ", "超过预载队列现在"),
    NOT_VIP("4005", "Please recharge VIP membership first ", "用户非vip"),

    NOT_ENOUGH_LUMENS_SUPPORT("4006", "Please recharge VIP membership and buy lumens first ", "用户不够dev权限"),

    NOT_TASK_EXIST("4007", "Task does not exist ", "任务已经被删除"),

    NOT_MODEL_SUPPORT("4008", "Model required ", "用户没有传modelId"),

    PIXELS_EXCEED_LIMIT("4009", "Pixels exceed limit", "像素超过限制"),

    CONTINUE_CREATE("4011", "Continue Create", "忽略告警，继续生图"),

    RELAX_CREATE_LIMIT("4012", "Relax Create Limit", "非会员，闲时生图达到上线"),

    UNKNOWN_ERROR("4999", "A generic error occurred during logic processing.", "处理逻辑时发生未知异常"),

    USER_PUBLIC_ACTIVITY_LIMIT("4013", "Your image has reached this activity limit.", "用户活动投稿超过次活动限制"),

    USER_ACTIVITY_TIME_PASSED("4014", "The deadline for submitting entries has passed.", "活动投稿时间已过"),

    USER_PUBLIC_RESUBMIT("4015", "Please do not resubmit.", "重复提交"),

    NOT_DELETE_WIN_IMAGE("4016", "This image has won an award and cannot be deleted.", "获奖图片不能删除"),
    BATCH_RMBG_TASK_LIMITED("4017", "A small system hiccup. Please try again later.", "批量去背景任务数已达上限"),

    BATCH_RMBG_BATCH_ID_EMPTY_ERROR("4018", "A small system hiccup. Please try again later.", "批量去背景batchId为空"),

    // Midjourney相关错误码
    MIDJOURNEY_API_ERROR("4019", "This image processing is failed. Please try again later", "Midjourney API调用失败"),

    COUPON_MAX_REDEMPTIONS("8001", "Coupon has reached the maximum number of redemptions.", "优惠券已达到最大兑换次数"),
    COUPON_EXPIRED("8002", "Coupon has expired.", "优惠券已过期"),
    COUPON_NOT_START("8004", "Coupon is not start.", "优惠券未开始"),
    COUPON_NOT_FOUND("8003", "Coupon not found.", "优惠券不存在"),
    COUPON_NOT_SUPPORT("8005", "Coupon not support.", "优惠券不支持"),
    MIDJOURNEY_PROMPT_CHECK_FAILED("4020", "Prompt check failed", "Prompt检查失败"),

    MIDJOURNEY_GENERATION_FAILED("4021", "This image processing is failed. Please try again later", "图像生成失败"),

    ILLEGAL_REQUEST("4022", "This image processing is failed. Please try again later", "非法请求"),

    MODEL_NOT_SUPPORT("4023", "This image processing is failed. Please try again later", "模型不支持"),

    MIDJOURNEY_ACTION_FAILED("4024", "This image processing is failed. Please try again later", "Midjourney操作失败"),

    TTAPI_EXCEED_CONCURRENT_JOBS("4025", "The model is currently experiencing high traffic. Please try again later.", "ttapi超过并发任务限制"),

    // Flux相关错误码
    FLUX_API_ERROR("4026", "This image processing is failed. Please try again later", "Flux API调用失败"),

    FLUX_EXCEED_CONCURRENT_JOBS("4027", "The model is currently experiencing high traffic. Please try again later.", "Flux超过并发任务限制"),

    FLUX_PROMPT_REQUIRED("4028", "prompt is required", "prompt不能为空"),

    // Gemini相关错误码
    GEMINI_API_ERROR("4029", "This image processing is failed. Please try again later", "Gemini API调用失败"),

    GEMINI_EXCEED_CONCURRENT_JOBS("4030", "The model is currently experiencing high traffic. Please try again later.", "Gemini超过并发任务限制"),

    GEMINI_PROMPT_REQUIRED("4031", "prompt is required", "prompt不能为空"),

    NOT_FOUND_COMM_FILE("4032", "Not found comm file", "社区图片不存在"),

    HAS_RENEWAL_SUBSCRIBER("4031", "cannot delete the current renewal subscriber", "不能删除当前有续订订阅用户"),

    EMAIL_FORMAT_ERROR("4032", "please use the correct email format", "邮箱格式不对"),

    USER_ACCOUNT_DELETED_OR_NOT_EXIST("4033", "this user does not exist or has been deleted", "账户已被删除"),
    // 已经参加过活动
    HAS_PARTICIPATED("1001", "You have already participated in the activity", "已经参加过活动"),
    // 不满足活动条件
    NOT_MEET_ACTIVITY_CONDITIONS("1000", "Not meet activity conditions", "不满足活动条件"),

    // 折扣资源已用完
    DISCOUNT_RESOURCE_USED_UP("1002", "Discount resource used up", "折扣资源已用完"),
    //    活动未开始或已结束
    ACTIVITY_NOT_STARTED_OR_ENDED("1003", "Activity not started or ended", "活动未开始或已结束"),

    IMAGE_NOT_SUPPORT("4060", "Image not support !", "不支持的图片"),

    // fal.ai API 具体错误码
    FAL_INTERNAL_SERVER_ERROR("6040", "Internal server error occurred. Please try again later.", "服务器内部错误，请稍后重试"),

    FAL_GENERATION_TIMEOUT("6041", "Generation timeout. Please try again later.", "生成超时，请稍后重试"),

    FAL_DOWNSTREAM_SERVICE_ERROR("6042", "Downstream service error. Please try again later.", "下游服务错误，请稍后重试"),

    FAL_DOWNSTREAM_SERVICE_UNAVAILABLE("6043", "Downstream service unavailable. Please try again later.", "下游服务不可用，请稍后重试"),

    FAL_CONTENT_POLICY_VIOLATION("6044", "Content violates usage policies. Please modify your input.", "内容违反使用政策，请修改您的输入"),

    FAL_IMAGE_TOO_SMALL("6045", "Image dimensions are too small. Please use a larger image.", "图片尺寸太小，请使用更大的图片"),

    FAL_IMAGE_TOO_LARGE("6046", "Image dimensions are too large. Please use a smaller image.", "图片尺寸太大，请使用更小的图片"),

    FAL_IMAGE_LOAD_ERROR("6047", "Failed to load the image. Please check the image format and try again.", "图片加载失败，请检查图片格式后重试"),

    FAL_FILE_DOWNLOAD_ERROR("6048", "Failed to download the file. Please check the URL and try again.", "文件下载失败，请检查URL后重试"),

    FAL_FACE_DETECTION_ERROR("6049", "Could not detect face in the image. Please use an image with a clear face.", "图片中未检测到人脸，请使用包含清晰人脸的图片"),

    FAL_FILE_TOO_LARGE("6050", "File size is too large. Please use a smaller file.", "文件大小超限，请使用更小的文件"),

    FAL_GREATER_THAN("6051", "Input value should be greater than the specified threshold.", "输入值应大于指定阈值"),

    FAL_GREATER_THAN_EQUAL("6052", "Input value should be greater than or equal to the specified threshold.", "输入值应大于等于指定阈值"),

    FAL_LESS_THAN("6053", "Input value should be less than the specified threshold.", "输入值应小于指定阈值"),

    FAL_LESS_THAN_EQUAL("6054", "Input value should be less than or equal to the specified threshold.", "输入值应小于等于指定阈值"),

    FAL_MULTIPLE_OF("6055", "Input value should be a multiple of the specified factor.", "输入值应为指定因子的倍数"),

    FAL_SEQUENCE_TOO_SHORT("6056", "Sequence is too short. Please provide more items.", "序列太短，请提供更多项目"),

    FAL_SEQUENCE_TOO_LONG("6057", "Sequence is too long. Please provide fewer items.", "序列太长，请减少项目数量"),

    FAL_ONE_OF("6058", "Input value is not among the allowed values.", "输入值不在允许的值范围内"),

    FAL_FEATURE_NOT_SUPPORTED("6059", "The requested feature is not supported.", "请求的功能不受支持"),

    FAL_INVALID_ARCHIVE("6060", "Invalid archive format. Please provide a valid archive file.", "无效的压缩包格式，请提供有效的压缩包文件"),

    FAL_ARCHIVE_FILE_COUNT_BELOW_MINIMUM("6061", "Archive contains too few files. Please add more files.", "压缩包文件数量不足，请添加更多文件"),

    FAL_ARCHIVE_FILE_COUNT_EXCEEDS_MAXIMUM("6062", "Archive contains too many files. Please remove some files.", "压缩包文件数量过多，请删除部分文件"),

    FAL_AUDIO_DURATION_TOO_LONG("6063", "Audio duration is too long. Please use a shorter audio file.", "音频时长过长，请使用更短的音频文件"),

    FAL_AUDIO_DURATION_TOO_SHORT("6064", "Audio duration is too short. Please use a longer audio file.", "音频时长过短，请使用更长的音频文件"),

    FAL_UNSUPPORTED_AUDIO_FORMAT("6065", "Unsupported audio format. Please use a supported format.", "不支持的音频格式，请使用支持的格式"),

    FAL_UNSUPPORTED_IMAGE_FORMAT("6066", "Unsupported image format. Please use a supported format.", "不支持的图片格式，请使用支持的格式"),

    FAL_UNSUPPORTED_VIDEO_FORMAT("6067", "Unsupported video format. Please use a supported format.", "不支持的视频格式，请使用支持的格式"),

    FAL_VIDEO_DURATION_TOO_LONG("6068", "Video duration is too long. Please use a shorter video file.", "视频时长过长，请使用更短的视频文件"),

    FAL_VIDEO_DURATION_TOO_SHORT("6069", "Video duration is too short. Please use a longer video file.", "视频时长过短，请使用更长的视频文件");


    private final String code;
    private final String message;
    private final String description;

    LogicErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
