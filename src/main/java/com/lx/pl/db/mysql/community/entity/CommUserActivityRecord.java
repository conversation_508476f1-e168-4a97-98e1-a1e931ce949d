package com.lx.pl.db.mysql.community.entity;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

@Data
@ToString
@Document(collection = "user_activity_record")
public class CommUserActivityRecord {

    private Long userId;

    private String loginName;

    private Set<Long> activityIds;

    private Set<Long> activityIosId;

    private Set<Long> activityWebId;

    private Set<Long> activityAndroidId;



}
