package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@TableName("gpt_user")
@Data
public class User extends MyBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 角色id
     */
    private Integer roleId;

    /**
     * microsoft登录唯一标识
     */
    private String microsoftId;

    /**
     * apple登录唯一标识
     */
    private String appleId;

    /**
     * facebook登录唯一标识
     */
    private String facebookId;

    /**
     * google登录唯一标识
     */
    private String googleId;

    /**
     * 微信小程序唯一标识
     */
    private String wechatOpenId;

    /**
     * 登录账号
     */
    private String loginName;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 密码
     */
    private String password;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 重置密码验证码
     */
    private String resetPasswordVcode;

    /**
     * 图片公开还是私有(img_public : 公有 img_private ： 私有)
     */
    private String visibility;

    /**
     * 今天之前用户生成图片数量（以零时区为准）
     */
    private Integer totalImgNum;

    /**
     * 是否清除提示词
     */
    private Boolean clearPrompt;

    /**
     * 用户是否进入了黑名单
     */
    private Boolean blackListFlag;

    /**
     * 头像文件名称
     */
    private String avatarName;

    /**
     * 头像缩略图文件名称
     */
    private String thumbnailAvatarName;

    /**
     * 头像文件路径
     */
    private String avatarUrl;

    /**
     * 头像文件缩略图路径
     */
    private String thumbnailAvatarUrl;

    /**
     * 用户已经上传图片到相册数量
     */
    private Long albumImgNum;

    /**
     * 用户反馈邮箱
     */
    private String feedbackEmail;

    /**
     * 用户多语言设置
     */
    private String localLang;

    /**
     * 注册用户所在国家
     */
    private String registCountry;

    /**
     * 反馈黑名单标识
     */
    private Boolean contactBlackListFlag;

    /**
     * 自我介绍
     */
    private String introduction;

    /**
     * 已使用的存储空间 bytes
     */
    private Long usedSize;

    /**
     * 总存储空间 bytes
     */
    private Long totalSize;


    /**
     * 用户已收藏数量
     */
    private Integer usedCollectNum;

    /**
     * 用户能收藏总数量
     */
    private Integer totalCollectNum;

    /**
     * 会员类型： basic 非会员 standard 普通会员 pro 高级会员
     */
    private String vipType;

    /**
     * 价格间隔 年: year，月: month
     */
    private String priceInterval;

    /**
     * 普通会员生效时间
     */
    private Long vipBeginTime;

    /**
     * 普通会员过期时间
     */
    private Long vipEndTime;


    /**
     * 每日免费点数日期(零时区开始时间戳)
     */
    private Long dailyLumensTime;

    /**
     * 每日免费点数
     */
    private Integer dailyLumens;


    /**
     * 剩余每日免费点数
     */
    private Integer useDailyLumens;


    /**
     * 用户配置相关
     */
    private JsonNode userConfig;

    /**
     * ios 原生设备token
     */
    private String iosDeviceToken;

    /**
     * android 原生设备token
     */
    private String androidDeviceToken;


    /**
     * ios设备token
     */
    private String iosFcmToken;

    /**
     * android设备token
     */
    private String androidFcmToken;

    /**
     * 每日更新
     */
    @TableField(exist = false)
    private Boolean dailyUpdate;

    /**
     * 系统奖励点数，包含任务完成奖励
     */
    private Integer systemRewardLumen;

    private String ip;
}
