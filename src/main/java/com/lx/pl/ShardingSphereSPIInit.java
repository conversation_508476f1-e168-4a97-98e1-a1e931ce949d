package com.lx.pl;

import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.spi.ShardingSphereServiceLoader;
import org.apache.shardingsphere.spi.annotation.SingletonSPI;
import org.reflections.Reflections;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;
/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ShardingSphereSPIInit {

    @PostConstruct
    public void initSPI() {
        Reflections reflections = new Reflections("org.apache.shardingsphere");
        Set<Class<?>> singletonSPIClasses = reflections.getTypesAnnotatedWith(SingletonSPI.class);
        log.debug("init SPI size: {}", singletonSPIClasses.size());
        for (Class<?> clazz : singletonSPIClasses) {
            log.debug("init SPI: {}", clazz.getName());
            ShardingSphereServiceLoader.register(clazz);
        }
        log.info("init SPI done");
    }
}
