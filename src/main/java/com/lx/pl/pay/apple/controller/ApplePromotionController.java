package com.lx.pl.pay.apple.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.apple.domain.ApplePopupConfig;
import com.lx.pl.pay.apple.domain.AppleProductPromotion;
import com.lx.pl.pay.apple.dto.AppleActivityConfigVo;
import com.lx.pl.pay.apple.service.AppleActivityConfigService;
import com.lx.pl.pay.apple.service.ApplePopupConfigService;
import com.lx.pl.pay.apple.service.AppleProductPromotionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/apple-promotion")
@Tag(name = "Apple 活动/促销配置")
@RequiredArgsConstructor
public class ApplePromotionController {

    private final AppleActivityConfigService activityService;
    private final AppleProductPromotionService promotionService;
    private final ApplePopupConfigService popupService;

    @Operation(summary = "查询活动配置列表")
    @GetMapping("/activity/list")
    @Authorization
    public R<List<AppleActivityConfigVo>> listActivity(@CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {
        log.info("listActivity start: {}", user.getLoginName());
        String platform = request.getHeader("Platform");
        return R.success(activityService.listActivity(user, platform));
    }

    @Operation(summary = "查询产品促销列表")
    @GetMapping("/product-promotion/list")
    @Authorization
    public R<List<AppleProductPromotion>> listPromotion(@RequestParam(required = true, value = "productId") String productId,
                                                        @RequestParam(required = false) Boolean enable) {
        LambdaQueryWrapper<AppleProductPromotion> qw = new LambdaQueryWrapper<>();
        qw.eq(AppleProductPromotion::getRelationId, productId);
        if (enable != null) {
            qw.eq(AppleProductPromotion::getEnable, enable);
            qw.gt(AppleProductPromotion::getDeadline, System.currentTimeMillis() / 1000);
        }
        qw.orderByDesc(AppleProductPromotion::getCreateTime);
        return R.success(promotionService.list(qw));
    }

    /**
     * 查询弹窗配置
     */
    @Operation(summary = "查询弹窗配置")
    @GetMapping("/popup/list")
    @Authorization
    public R<List<ApplePopupConfig>> listPopup() {
        return R.success(popupService.listPopup());
    }
}

