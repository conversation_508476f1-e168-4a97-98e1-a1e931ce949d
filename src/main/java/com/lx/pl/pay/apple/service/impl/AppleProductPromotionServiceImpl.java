package com.lx.pl.pay.apple.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.pay.apple.domain.AppleProductPromotion;
import com.lx.pl.pay.apple.mapper.AppleProductPromotionMapper;
import com.lx.pl.pay.apple.service.AppleProductPromotionService;
import com.lx.pl.pay.apple.service.PayAppleProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AppleProductPromotionServiceImpl extends ServiceImpl<AppleProductPromotionMapper, AppleProductPromotion>
        implements AppleProductPromotionService {

    @Autowired
    private PayAppleProductService appleProductService;
    @Override
    public List<AppleProductPromotion> queryByOff(int off) {
        if (off > 0) {
            return this.lambdaQuery().
                    eq(AppleProductPromotion::getOff, off)
                    .eq(AppleProductPromotion::getEnable, true)
                    .gt(AppleProductPromotion::getDeadline, System.currentTimeMillis() / 1000)
                    .list();
        }
        return List.of();
    }

    @Override
    public List<AppleProductPromotion> queryByOffAndPlanLevel(String planLevel, String priceInterval, int off) {
        PayAppleProduct one = appleProductService.lambdaQuery()
                .eq(PayAppleProduct::getPlanLevel, planLevel)
                .eq(PayAppleProduct::getPriceInterval, priceInterval)
                .eq(PayAppleProduct::getStatus, true)
                .one();
        if (one != null) {
            return this.lambdaQuery().
                    eq(AppleProductPromotion::getOff, off)
                    .eq(AppleProductPromotion::getRelationId, one.getId())
                    .eq(AppleProductPromotion::getEnable, true)
                    .gt(AppleProductPromotion::getDeadline, System.currentTimeMillis() / 1000)
                    .list();
        }
        return List.of();
    }
}

