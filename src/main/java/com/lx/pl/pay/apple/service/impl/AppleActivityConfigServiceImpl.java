package com.lx.pl.pay.apple.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.domain.AppleActivityConfig;
import com.lx.pl.pay.apple.dto.AppleActivityConfigVo;
import com.lx.pl.pay.apple.mapper.AppleActivityConfigMapper;
import com.lx.pl.pay.apple.service.AppleActivityConfigService;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.PayLotteryLogService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class AppleActivityConfigServiceImpl extends ServiceImpl<AppleActivityConfigMapper, AppleActivityConfig>
        implements AppleActivityConfigService {

    @Autowired
    private PayLotteryLogService payLotteryLogService;

    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;

    @Override
    public List<AppleActivityConfigVo> listActivity(User user, String platform) {
        long currentTime = System.currentTimeMillis() / 1000;
        List<AppleActivityConfig> list = this.lambdaQuery()
                .eq(AppleActivityConfig::getEnable, true)
                .lt(AppleActivityConfig::getStartTime, currentTime)
                .gt(AppleActivityConfig::getDeadline, currentTime)
                .list();
        if (CollUtil.isEmpty(list)) {
            return List.of();
        }
        boolean hasVip;
        List<SubscriptionCurrent> logicValidSubscriptionsFromDb = subscriptionCurrentService.getLogicValidSubscriptionsFromDb(user.getId());
        if (CollUtil.isNotEmpty(logicValidSubscriptionsFromDb)) {
            List<SubscriptionCurrent> collect = logicValidSubscriptionsFromDb.stream().filter(subscription -> {
                return subscription.getVipPlatform().equalsIgnoreCase(VipPlatform.GIFT.getPlatformName());
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                hasVip = true;
            } else {
                hasVip = false;
            }
        } else {
            hasVip = false;
        }

        List<AppleActivityConfigVo> voList = list.stream().map(item -> {
            if (payLotteryLogService.hasParticipated(user.getId(), platform, item.getCode())) {
                return null;
            }
            if ("sweep_stakes".equals(item.getCode()) && hasVip) {
                return null;
            }
            AppleActivityConfigVo vo = new AppleActivityConfigVo();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return voList;
    }
}

