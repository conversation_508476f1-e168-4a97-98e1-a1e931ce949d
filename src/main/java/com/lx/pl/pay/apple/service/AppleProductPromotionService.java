package com.lx.pl.pay.apple.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.apple.domain.AppleProductPromotion;

import java.util.List;

public interface AppleProductPromotionService extends IService<AppleProductPromotion> {
    List<AppleProductPromotion> queryByOff(int off);

    List<AppleProductPromotion> queryByOffAndPlanLevel(String planLevel, String priceInterval, int off);
}

