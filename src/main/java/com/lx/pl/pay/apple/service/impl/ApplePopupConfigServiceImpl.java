package com.lx.pl.pay.apple.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.apple.domain.ApplePopupConfig;
import com.lx.pl.pay.apple.mapper.ApplePopupConfigMapper;
import com.lx.pl.pay.apple.service.ApplePopupConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApplePopupConfigServiceImpl extends ServiceImpl<ApplePopupConfigMapper, ApplePopupConfig> implements ApplePopupConfigService {

    @Override
    public List<ApplePopupConfig> listPopup() {
        return this.lambdaQuery()
                .eq(ApplePopupConfig::getEnable, true)
                .orderByDesc(ApplePopupConfig::getCreateTime).list();
    }
}