package com.lx.pl.pay.apple.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * apple_activity_config 表实体
 */
@Data
@TableName("pay_apple_activity_config")
@Schema(description = "Apple 活动配置")
public class AppleActivityConfig extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "活动编码")
    private String code;

    @Schema(description = "是否启用")
    private Boolean enable;

    // 数据库列名为 startTime（非下划线），显式指定
    @TableField("startTime")
    @Schema(description = "开始日期 (秒时间戳)")
    private Long startTime;

    @Schema(description = "截止日期 (秒时间戳)")
    private Long deadline;
}

