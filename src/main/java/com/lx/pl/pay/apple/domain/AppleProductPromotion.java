package com.lx.pl.pay.apple.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * apple_product_promotion 表实体
 */
@Data
@TableName("pay_apple_product_promotion")
@Schema(description = "Apple 产品促销配置")
public class AppleProductPromotion extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "product主键id")
    private Long relationId;

    private String productId;

    @Schema(description = "OFF")
    private Integer off;

    @Schema(description = "折扣链接")
    private String url;

    @Schema(description = "折扣码")
    private String code;

    @Schema(description = "启用状态")
    private Boolean enable;

    @Schema(description = "截止日期 (秒时间戳)")
    private Long deadline;
}

