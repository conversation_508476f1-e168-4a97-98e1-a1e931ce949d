package com.lx.pl.pay.apple.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * apple_activity_config 表实体
 */
@Data
@TableName("pay_apple_popup_config")
@Schema(description = "Apple 弹窗")
public class ApplePopupConfig extends MyBaseEntity {
    /**
     * CREATE TABLE `apple_popup_config` (
     * `id` bigint NOT NULL COMMENT 'ID',
     * `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动名称',
     * `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动编码',
     * `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '内容',
     * `btn_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '按钮名称',
     * `btn_jump` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '按钮跳转',
     * `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本号',
     * `enable` tinyint(1) DEFAULT NULL COMMENT '是否启用',
     * `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     * `update_time` datetime DEFAULT NULL COMMENT '更新时间',
     * `create_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
     * `update_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者',
     * PRIMARY KEY (`id`)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
     */
    @Schema(description = "ID")
    private Long id;
    @Schema(description = "标题")
    private String title;
    @Schema(description = "图片地址")
    private String imgUrl;
    @Schema(description = "内容")
    private String content;
    @Schema(description = "按钮名称")
    private String btnName;
    @Schema(description = "按钮跳转")
    private String btnJump;
    @Schema(description = "版本号")
    private String version;
    @Schema(description = "是否启用")
    private Boolean enable;
}

