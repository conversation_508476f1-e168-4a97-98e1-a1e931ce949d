package com.lx.pl.pay.common.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import org.redisson.Redisson;
import org.redisson.api.RDeque;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
public class RedisPrizePool {

    private final RedissonClient redisson;
    private final String poolName;
    private final String lockName;
    private final String statsKey;
    private final String[] prizes;
    private final int[] weights;
    private final int totalWeight;

    public RedisPrizePool(String redisAddr, String[] prizes, int[] weights) {
        Config config = new Config();
        config.useSingleServer().setAddress(redisAddr);
        this.redisson = Redisson.create(config);

        this.poolName = "prizePool";
        this.lockName = "prizeLock";
        this.statsKey = "prizeStats";
        this.prizes = prizes;
        this.weights = weights;

        this.totalWeight = Arrays.stream(weights).sum();
    }

    /**
     * 抽奖
     */
    public String draw() {
        RDeque<String> deque = redisson.getDeque(poolName);
        String prize = deque.pollFirst();

        if (prize == null) {
            refill();
            prize = deque.getFirst();
        }

        if (prize != null) {
            // 更新统计
            redisson.getMap(statsKey).merge(prize, 1, (oldVal, newVal) -> (Integer) oldVal + 1);
            refill();
        }

        return prize;
    }

    /**
     * 批量 refill（一次性生成 multiRounds * totalWeight 个奖品）
     */
    private void refill() {
        RLock lock = redisson.getLock(lockName);
        if (lock.tryLock()) {
            try {
                RDeque<String> deque = redisson.getDeque(poolName);
                if (deque.isEmpty()) {
                    // 一次生成5轮奖品
                    int multiRounds = 1;
                    List<String> bag = new ArrayList<>(multiRounds * totalWeight);

                    for (int r = 0; r < multiRounds; r++) {
                        for (int i = 0; i < prizes.length; i++) {
                            for (int j = 0; j < weights[i]; j++) {
                                bag.add(prizes[i]);
                            }
                        }
                    }
                    // 打乱
                    Collections.shuffle(bag, ThreadLocalRandom.current());
                    deque.addAll(bag);
                }
            } finally {
                lock.unlock();
            }
        }
    }

    public void printStats() {
        Map<String, Integer> stats = (Map<String, Integer>) (Map) redisson.getMap(statsKey);
        // 打印概率
        int totalCount = 0;
        for (int count : stats.values()) {
            totalCount += count;
        }
        for (Map.Entry<String, Integer> entry : stats.entrySet()) {
            System.out.println("Discount " + entry.getKey() + ": " + entry.getValue() / (double) totalCount * 100 + "%");
        }

        System.out.println("统计结果：" + stats);
    }

    public void shutdown() {
        redisson.shutdown();
    }

    public static void main(String[] args) throws InterruptedException {
        Logger redissonLogger = (Logger) LoggerFactory.getLogger("org.redisson");
        redissonLogger.setLevel(Level.INFO);

        // 关闭 Netty debug
        Logger nettyLogger = (Logger) LoggerFactory.getLogger("io.netty");
        nettyLogger.setLevel(Level.INFO);
        String[] prizes = {"90", "60", "100", "500_lumen", "none"};
        int[] weights = {5, 68, 2, 5, 20};

        RedisPrizePool pool = new RedisPrizePool("redis://127.0.0.1:6379", prizes, weights);

        int threadCount = 50;
        int drawCountPerThread = 2;

        Runnable task = () -> {
            for (int i = 0; i < drawCountPerThread; i++) {
                pool.draw();
            }
        };

        List<Thread> threads = new ArrayList<>();
        long start = System.currentTimeMillis();
        for (int t = 0; t < threadCount; t++) {
            Thread th = new Thread(task);
            threads.add(th);
            th.start();
        }
        for (Thread th : threads) {
            th.join();
        }
        long end = System.currentTimeMillis();

        System.out.println("耗时: " + (end - start) + " ms");
        pool.printStats();
        pool.shutdown();
    }
}
