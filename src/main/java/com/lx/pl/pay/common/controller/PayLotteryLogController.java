package com.lx.pl.pay.common.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.common.domain.PayLotteryLog;
import com.lx.pl.pay.common.service.PayLotteryLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/pay-lottery-log")
@Tag(name = "抽奖记录")
@RequiredArgsConstructor
public class PayLotteryLogController {

    private final PayLotteryLogService payLotteryLogService;

    @Operation(summary = "新增或更新抽奖记录")
    @PostMapping
    @Authorization
    public R<Boolean> save(@RequestBody PayLotteryLog log, @CurrentUser @Parameter(hidden = true) User user) {
        if (log.getUserId() == null) {
            log.setUserId(user.getId());
            log.setLoginName(user.getLoginName());
        }
        return R.success(payLotteryLogService.saveOrUpdate(log));
    }

    @Operation(summary = "根据当前用户查询抽奖记录")
    @GetMapping("/mine")
    @Authorization
    public R<List<PayLotteryLog>> myLogs(@CurrentUser @Parameter(hidden = true) User user) {
        List<PayLotteryLog> list = payLotteryLogService.list(new LambdaQueryWrapper<PayLotteryLog>()
                .eq(PayLotteryLog::getUserId, user.getId())
                .orderByDesc(PayLotteryLog::getCreateTime));
        return R.success(list);
    }

    @Operation(summary = "删除记录")
    @DeleteMapping("/{id}")
    @Authorization
    public R<Boolean> remove(@PathVariable Long id) {
        return R.success(payLotteryLogService.removeById(id));
    }
}

