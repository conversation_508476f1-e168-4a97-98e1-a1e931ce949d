package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.BackendFallbackFactory;
import com.lx.pl.dto.*;
import com.lx.pl.interceptor.TokenInterceptor;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;

import java.util.List;


@RetrofitClient(baseUrl = "${backend.baseUrl}", fallbackFactory = BackendFallbackFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface BackendApi {

    /**
     * 生图
     *
     * @param
     * @return 返回生图信息
     */
    @POST("/api/gen/prompt")
    Response<BackendPromptResult> createPicture(@Body BackendPromptParams requestParam);


    /**
     * 获取模型列表
     *
     * @param
     * @return 返回模型列表信息
     */
    @GET("/api/common/config")
    Response<ModelInformation> getModelInformation();

    /**
     * 去除背景图片
     *
     * @param
     * @return 返回去除背景结果
     */
    @POST("/api/gen/rembg")
    Response<BackendPromptResult> rembgPicture(@Body RembgParams rembgParams);

    /**
     * 高清修复图片
     *
     * @param
     * @return 返回高清修复结果
     */
    @POST("/api/gen/hires-fix")
    Response<BackendPromptResult> fixPicture(@Body HiresFixParams hiresFixParams);

    /**
     * 上传的图片进行超分
     * @param upscaleParams
     * @return
     */
    @POST("/api/gen/upscale")
    Response<BackendPromptResult> upscale(@Body UpscaleParams upscaleParams);

    /**
     * 局部重绘
     *
     * @param localRedrawParams
     * @return 返回局部重绘结果
     */
    @POST("/api/gen/local_redraw")
    Response<BackendPromptResult> localRedraw(@Body LocalRedrawPromptParams localRedrawParams);

    /**
     * 扩图
     *
     * @param enlargeImageParams
     * @return 返回扩图结果
     */
    @POST("/api/gen/enlarge_image")
    Response<BackendPromptResult> enlargeImage(@Body EnlargeImageParams enlargeImageParams);

    /**
     * 线稿上色
     *
     * @param lineRecolorParams
     * @return 返回上色结果
     */
    @POST("/api/gen/line_recolor")
    Response<BackendPromptResult> lineRecolor(@Body LineRecolorParams lineRecolorParams);

    /**
     * 状态
     *
     * @param
     * @return 返回图片渐变结果
     */
    @POST("/api/gen/vary")
    Response<BackendPromptResult> vary(@Body VaryParams pgParams);

    /**
     * 状态
     *
     * @param
     * @return 返回服务器当前状态
     */
    @GET("/api/common/resource_state")
    Response<List<ServerStatus>> getServerStatus();

    /**
     * 视频生成
     *
     * @param videoParams
     * @return 返回视频生成结果
     */
    @POST("/api/gen/video-generation")
    Response<BackendPromptResult> videoGeneration(@Body VideoGenerationParams videoParams);

}
