package com.lx.pl.util;

import com.lx.pl.dto.Resolution;
import lombok.extern.slf4j.Slf4j;

/**
 * 视频生成Lumen定价工具类
 *
 * <AUTHOR> Assistant
 * @date 2025-08-21
 */
@Slf4j
public class VideoLumenUtil {

    /**
     * 根据视频类型和尺寸计算Lumen成本
     *
     * @param videoType  视频类型: text_to_video 或 image_to_video
     * @param resolution 视频尺寸
     * @return Lumen成本
     */
    public static int calculateVideoLumenCost(String videoType, Resolution resolution) {
        if ("image_to_video".equals(videoType)) {
            // 图生视频统一45 lumens
            return 45;
        } else if ("text_to_video".equals(videoType)) {
            // 文生视频根据尺寸计算
            return calculateTextToVideoLumenCost(resolution.getWidth(), resolution.getHeight());
        }

        // 默认返回25 lumens
        log.warn("未知的视频类型: {}, 使用默认定价25 lumens", videoType);
        return 25;
    }

    /**
     * 计算文生视频的Lumen成本
     *
     * @param width  视频宽度
     * @param height 视频高度
     * @return Lumen成本
     */
    private static int calculateTextToVideoLumenCost(Integer width, Integer height) {
        if (width == null || height == null) {
            // 默认尺寸按25 lumens计算
            return 25;
        }

        // 计算宽高比
        String aspectRatio = getAspectRatio(width, height);

        switch (aspectRatio) {
            case "4:3":   // 640*480
            case "3:2":   // 720*480
            case "1:1":   // 768*768
            case "2:3":   // 480*720
            case "3:4":   // 480*640
                return 25;
            case "16:9":  // 1280*720
            case "9:16":  // 720*1280
                return 45;
            default:
                // 未知尺寸，根据像素数量判断
                return calculateByPixelCount(width, height);
        }
    }

    /**
     * 根据像素数量计算定价
     *
     * @param width  宽度
     * @param height 高度
     * @return Lumen成本
     */
    private static int calculateByPixelCount(int width, int height) {
        long pixelCount = (long) width * height;

        // 根据像素数量判断，高像素使用45 lumens，低像素使用25 lumens
        // 以720*720 = 518400像素作为分界线
        if (pixelCount > 600000) {
            return 45;
        } else {
            return 25;
        }
    }

    /**
     * 获取宽高比字符串
     *
     * @param width  宽度
     * @param height 高度
     * @return 宽高比字符串
     */
    private static String getAspectRatio(int width, int height) {
        // 计算最大公约数
        int gcd = gcd(width, height);
        int ratioWidth = width / gcd;
        int ratioHeight = height / gcd;

        return ratioWidth + ":" + ratioHeight;
    }
    
    /**
     * 计算最大公约数
     *
     * @param a 数字a
     * @param b 数字b
     * @return 最大公约数
     */
    private static int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }

    /**
     * 验证是否为支持的文生视频尺寸
     *
     * @param width  宽度
     * @param height 高度
     * @return 是否支持
     */
    public static boolean isSupportedTextToVideoSize(int width, int height) {
        // 支持的尺寸列表
        return (width == 640 && height == 480) ||   // 4:3
                (width == 720 && height == 480) ||   // 3:2
                (width == 1280 && height == 720) ||  // 16:9
                (width == 768 && height == 768) ||   // 1:1
                (width == 720 && height == 1280) ||  // 9:16
                (width == 480 && height == 720) ||   // 2:3
                (width == 480 && height == 640);     // 3:4
    }

    /**
     * 获取支持的文生视频尺寸描述
     *
     * @param width  宽度
     * @param height 高度
     * @return 尺寸描述
     */
    public static String getVideoSizeDescription(int width, int height) {
        if (width == 640 && height == 480) return "4:3 (640×480)";
        if (width == 720 && height == 480) return "3:2 (720×480)";
        if (width == 1280 && height == 720) return "16:9 (1280×720)";
        if (width == 768 && height == 768) return "1:1 (768×768)";
        if (width == 720 && height == 1280) return "9:16 (720×1280)";
        if (width == 480 && height == 720) return "2:3 (480×720)";
        if (width == 480 && height == 640) return "3:4 (480×640)";

        return "自定义 (" + width + "×" + height + ")";
    }

    /**
     * 获取推荐的视频尺寸列表
     *
     * @return 推荐尺寸数组 [width, height, lumens, description]
     */
    public static int[][] getRecommendedVideoSizes() {
        return new int[][]{
                {640, 480, 25, 0},    // 4:3, 25 lumens, index 0
                {720, 480, 25, 1},    // 3:2, 25 lumens, index 1
                {1280, 720, 45, 2},   // 16:9, 45 lumens, index 2
                {768, 768, 25, 3},    // 1:1, 25 lumens, index 3
                {720, 1280, 45, 4},   // 9:16, 45 lumens, index 4
                {480, 720, 25, 5},    // 2:3, 25 lumens, index 5
                {480, 640, 25, 6}     // 3:4, 25 lumens, index 6
        };
    }
}
