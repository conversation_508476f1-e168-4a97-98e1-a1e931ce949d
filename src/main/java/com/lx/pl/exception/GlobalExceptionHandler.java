package com.lx.pl.exception;

import com.lx.pl.dto.generic.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 密码错误异常
     */
    @ExceptionHandler(UserPasswordNotMatchException.class)
    @ResponseBody
    public R<String> handleUserPasswordNotMatchException(UserPasswordNotMatchException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}', 异常：'{}'", requestURI, e.getMessage());
        return R.fail(400, e.getMessage());
    }

    @ExceptionHandler(OperationNotAllowedException.class)
    @ResponseBody
    public R<String> handleOperationNotAllowedException(OperationNotAllowedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}', 异常：'{}'", requestURI, e.getMessage());
        return R.fail(400, e.getMessage());
    }

    @ExceptionHandler(ServerInternalException.class)
    @ResponseBody
    public R<String> handleServerInternalException(ServerInternalException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}', 错误：'{}'", requestURI, e.getMessage());
        return R.fail(400, e.getMessage());
    }

    @ExceptionHandler(BadRequestException.class)
    @ResponseBody
    public R<String> handleBadRequestException(BadRequestException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}', 错误：'{}'", requestURI, e.getMessage());
        return R.fail(400, e.getMessage());
    }

    @ExceptionHandler(LogicDoException.class)
    public R<String> handleLogicDoException(LogicDoException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}', 错误：'{}'", requestURI, e.getMessage());
        return R.fail(601, e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<String> handleValidationExceptions(MethodArgumentNotValidException ex) {
        StringBuilder errors = new StringBuilder();
        ex.getBindingResult().getAllErrors().forEach(error -> errors.append(error.getDefaultMessage()).append("; "));
        return R.fail(400, errors.toString());
    }

    /**
     * @Description: 支付相关异常处理
     * @Param: [ex]
     * @return: com.lx.pl.dto.generic.R<java.lang.String>
     * @Author: senlin_he
     * @Date: 2024/12/26
     */
    @ExceptionHandler(PaymentException.class)
    public R<String> handlePaymentExceptions(PaymentException ex) {
        String errorCode = ex.getCode();
        String errorMessage = ex.getMessage();
        log.warn("支付异常代码: {}, 消息: {}", errorCode, errorMessage);
        return R.fail(Integer.parseInt(errorCode), errorMessage);
    }


    @ExceptionHandler(PayAppleException.class)
    public R<String> handlePaymentExceptions(PayAppleException ex) {
        String errorCode = ex.getCode();
        String errorMessage = ex.getMessage();
        log.warn("apple-支付异常代码: {}, 消息: {}", errorCode, errorMessage);
        return R.fail(Integer.parseInt(errorCode), errorMessage);
    }

    @ExceptionHandler(PayPalException.class)
    public R<String> handlePaymentExceptions(PayPalException ex) {
        String errorCode = ex.getCode();
        String errorMessage = ex.getMessage();
        log.warn("paypal-支付异常代码: {}, 消息: {}", errorCode, errorMessage);
        return R.fail(Integer.parseInt(errorCode), errorMessage);
    }

    /**
     * @Description: 新增逻辑异常处理
     * @Param: [ex]
     * @return: com.lx.pl.dto.generic.R<java.lang.String>
     * @Author: senlin_he
     * @Date: 2024/12/26
     */
    @ExceptionHandler(LogicException.class)
    public R<String> handleLogicExceptions(LogicException ex) {
        String errorCode = ex.getCode();
        String errorMessage = ex.getMessage();
        log.warn("逻辑异常代码: {}, 消息: {}", errorCode, errorMessage);
        return R.fail(Integer.parseInt(errorCode), errorMessage);
    }

    @ExceptionHandler(Exception.class)
    public R<String> handleException(Exception e, HttpServletRequest request) {
        log.warn("请求地址'{}', 发生系统异常. ", request.getRequestURI(), e);
        return R.fail(400, "Unknown error");
    }
}
