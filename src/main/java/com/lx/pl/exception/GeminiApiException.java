package com.lx.pl.exception;

import com.lx.pl.dto.gemini.GeminiResponse;
import com.lx.pl.enums.LogicErrorCode;
import lombok.Getter;

import java.util.List;

/**
 * Gemini API异常类
 * 用于处理fal.ai API返回的各种错误
 *
 * <AUTHOR>
 */
@Getter
public class GeminiApiException extends RuntimeException {

    /**
     * HTTP状态码
     */
    private final int statusCode;

    /**
     * fal.ai错误详情
     */
    private final List<GeminiResponse.FalError> errors;

    /**
     * 错误类型
     */
    private final String errorType;

    /**
     * 对应的LogicErrorCode
     */
    private final LogicErrorCode logicErrorCode;

    public GeminiApiException(int statusCode, List<GeminiResponse.FalError> errors) {
        super(buildMessage(errors));
        this.statusCode = statusCode;
        this.errors = errors;
        this.errorType = extractPrimaryErrorType(errors);
        this.logicErrorCode = mapToLogicErrorCode(this.errorType);
    }

    public GeminiApiException(int statusCode, String message) {
        super(message);
        this.statusCode = statusCode;
        this.errors = null;
        this.errorType = "unknown";
        this.logicErrorCode = LogicErrorCode.GEMINI_API_ERROR;
    }

    /**
     * 构建错误消息
     */
    private static String buildMessage(List<GeminiResponse.FalError> errors) {
        if (errors == null || errors.isEmpty()) {
            return "Unknown Gemini API error";
        }

        GeminiResponse.FalError primaryError = errors.get(0);
        return String.format("Gemini API Error [%s]: %s",
                primaryError.getType(), primaryError.getMsg());
    }

    /**
     * 提取主要错误类型
     */
    private static String extractPrimaryErrorType(List<GeminiResponse.FalError> errors) {
        if (errors == null || errors.isEmpty()) {
            return "unknown";
        }
        return errors.get(0).getType();
    }

    /**
     * 将fal.ai错误类型映射到LogicErrorCode
     */
    private static LogicErrorCode mapToLogicErrorCode(String errorType) {
        if (errorType == null) {
            return LogicErrorCode.GEMINI_API_ERROR;
        }

        switch (errorType) {
            case "internal_server_error":
                return LogicErrorCode.FAL_INTERNAL_SERVER_ERROR;
            case "generation_timeout":
                return LogicErrorCode.FAL_GENERATION_TIMEOUT;
            case "downstream_service_error":
                return LogicErrorCode.FAL_DOWNSTREAM_SERVICE_ERROR;
            case "downstream_service_unavailable":
                return LogicErrorCode.FAL_DOWNSTREAM_SERVICE_UNAVAILABLE;
            case "content_policy_violation":
                return LogicErrorCode.FAL_CONTENT_POLICY_VIOLATION;
            case "image_too_small":
                return LogicErrorCode.FAL_IMAGE_TOO_SMALL;
            case "image_too_large":
                return LogicErrorCode.FAL_IMAGE_TOO_LARGE;
            case "image_load_error":
                return LogicErrorCode.FAL_IMAGE_LOAD_ERROR;
            case "file_download_error":
                return LogicErrorCode.FAL_FILE_DOWNLOAD_ERROR;
            case "face_detection_error":
                return LogicErrorCode.FAL_FACE_DETECTION_ERROR;
            case "file_too_large":
                return LogicErrorCode.FAL_FILE_TOO_LARGE;
            case "greater_than":
                return LogicErrorCode.FAL_GREATER_THAN;
            case "greater_than_equal":
                return LogicErrorCode.FAL_GREATER_THAN_EQUAL;
            case "less_than":
                return LogicErrorCode.FAL_LESS_THAN;
            case "less_than_equal":
                return LogicErrorCode.FAL_LESS_THAN_EQUAL;
            case "multiple_of":
                return LogicErrorCode.FAL_MULTIPLE_OF;
            case "sequence_too_short":
                return LogicErrorCode.FAL_SEQUENCE_TOO_SHORT;
            case "sequence_too_long":
                return LogicErrorCode.FAL_SEQUENCE_TOO_LONG;
            case "one_of":
                return LogicErrorCode.FAL_ONE_OF;
            case "feature_not_supported":
                return LogicErrorCode.FAL_FEATURE_NOT_SUPPORTED;
            case "invalid_archive":
                return LogicErrorCode.FAL_INVALID_ARCHIVE;
            case "archive_file_count_below_minimum":
                return LogicErrorCode.FAL_ARCHIVE_FILE_COUNT_BELOW_MINIMUM;
            case "archive_file_count_exceeds_maximum":
                return LogicErrorCode.FAL_ARCHIVE_FILE_COUNT_EXCEEDS_MAXIMUM;
            case "audio_duration_too_long":
                return LogicErrorCode.FAL_AUDIO_DURATION_TOO_LONG;
            case "audio_duration_too_short":
                return LogicErrorCode.FAL_AUDIO_DURATION_TOO_SHORT;
            case "unsupported_audio_format":
                return LogicErrorCode.FAL_UNSUPPORTED_AUDIO_FORMAT;
            case "unsupported_image_format":
                return LogicErrorCode.FAL_UNSUPPORTED_IMAGE_FORMAT;
            case "unsupported_video_format":
                return LogicErrorCode.FAL_UNSUPPORTED_VIDEO_FORMAT;
            case "video_duration_too_long":
                return LogicErrorCode.FAL_VIDEO_DURATION_TOO_LONG;
            case "video_duration_too_short":
                return LogicErrorCode.FAL_VIDEO_DURATION_TOO_SHORT;
            default:
                return LogicErrorCode.GEMINI_API_ERROR;
        }
    }
}
