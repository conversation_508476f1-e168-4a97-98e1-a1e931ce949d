package com.lx.pl.exception;

import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.StripeErrorCode;

/**
 * 新增逻辑错误类
 */
public class LogicException extends BaseException {

    private static final long serialVersionUID = 1L;

    public LogicException(LogicErrorCode errorCode) {
        super("logic", errorCode.getCode(), null, errorCode.getMessage());
    }

    public LogicException(LogicErrorCode errorCode, String message) {
        super("logic", errorCode.getCode(), null, message);
    }

    public LogicException(LogicErrorCode errorCode, Throwable cause) {
        super("logic", errorCode.getCode(), null, String.valueOf(cause));
    }

}
