package com.lx.pl.interceptor;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.controller.optionLog.activity.UserActivityService;
import com.lx.pl.db.mysql.gen.entity.EventLog;
import com.lx.pl.manager.TokenManager;
import com.lx.pl.service.EventLogService;
import com.lx.pl.service.GeoIpCountryService;
import com.lx.pl.service.UserActionRecordService;
import com.lx.pl.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Component
public class LoggingInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(LoggingInterceptor.class);

    List<String> loginActions = Arrays.asList("login", "logout", "reset", "register", "create", "remove-background",
            "hires-fix", "local-redraw", "enlarge-image", "lineRecolor", "vary-image", "public-img");

    List<String> mongoLoginActions = Arrays.asList("task");

    private static final String AUTHORIZATION = "authorization";

    @Autowired
    EventLogService eventLogService;

    @Autowired
    GeoIpCountryService geoIpCountryService;

    @Autowired
    private TokenManager manager;

    @Autowired
    private UserActivityService userActivityService;
    @Resource
    private UserActionRecordService userActionRecordService;
    public static final String TRACE_ID = "TRACE_ID";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws JsonProcessingException {
        String action = StrUtil.subWithLength(request.getRequestURL().toString(), 0, 200);
        //从header中得到token
        String token = request.getHeader(AUTHORIZATION);
        if (manager.checkToken(token)) {
            long userId = manager.getUserId(token);
            String requestContent = JsonUtils.writeToString(request.getParameterMap());
            String requestIp = IpUtils.getIpAddr(request);
            String ipCountry = geoIpCountryService.getCountry(requestIp).orElse(LogicConstants.UNKNOWN_IP);
            // 记录用户行为
            userActionRecordService.record(userId, action, requestContent, requestIp, ipCountry, request.getHeader("platform"), getRequestBody(request).orElse(null));
            if (loginActions.stream().anyMatch(action::contains)) {
                //如果token验证成功，记录日志
                EventLog e = new EventLog();
                e.setUserId(userId);
                e.setAction(action);
                e.setUserAgent(request.getHeader("User-Agent"));
                e.setContent(requestContent);
                e.setCreateTime(LocalDateTime.now());
                e.setOperateIp(requestIp);
                e.setIpCountry(ipCountry);
                eventLogService.addEvent(e);
            }
            // 不为任务调度方面接口 则插入日活mongodb
            if (!mongoLoginActions.stream().anyMatch(action::contains)) {
                userActivityService.logUserActivity(Long.toString(userId), request);
            }
        } else {
            //未登录
            if (loginActions.stream().anyMatch(action::contains)) {
                EventLog e = new EventLog();
                e.setUserId(-1L);
                e.setAction(action);
                e.setUserAgent(request.getHeader("User-Agent"));
                e.setContent(JsonUtils.writeToString(request.getParameterMap()));
                e.setCreateTime(LocalDateTime.now());
                e.setOperateIp(IpUtils.getIpAddr(request));
                e.setIpCountry(geoIpCountryService.getCountry(e.getOperateIp()).get());
                eventLogService.addEvent(e);
            }
        }
        String tid = UUID.randomUUID().toString().replace("-", "");
        //可以考虑让客户端传入链路ID，但需保证一定的复杂度唯一性；如果没使用默认UUID自动生成
        if (!StringUtils.isEmpty(request.getHeader("TRACE_ID"))) {
            tid = request.getHeader("TRACE_ID");
        }
        MDC.put(TRACE_ID, tid);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        MDC.remove(TRACE_ID);
    }


    private Optional<String> getRequestBody(HttpServletRequest request) {
        Optional<String> requestBody = Optional.empty();
        try {
            if (request instanceof CachedBodyHttpServletRequest) {
                requestBody = Optional.of(((CachedBodyHttpServletRequest) request).getBodyAsString());
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting request body", e);
        }

        return requestBody;
    }
}
