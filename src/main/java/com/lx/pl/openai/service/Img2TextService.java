package com.lx.pl.openai.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import com.lx.pl.client.Img2GhibliApi;
import com.lx.pl.client.Img2TextApi;
import com.lx.pl.client.LanguageTranslateApi;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.Img2GhibliRequest;
import com.lx.pl.dto.ImgToGhibliRequest;
import com.lx.pl.dto.TranslateParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.LanguageCode;
import com.lx.pl.enums.LumenChangeSourceEnum;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.LogicDoException;
import com.lx.pl.openai.dto.CaptionRequest;
import com.lx.pl.openai.dto.CaptionResponse;
import com.lx.pl.openai.dto.captionResponseSub.CaptionResChoice;
import com.lx.pl.service.RedisService;
import com.lx.pl.service.VipService;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import com.lx.pl.util.UrlValidator;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RLock;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class Img2TextService {

    private static final String IMG_TO_GHIBLI_QUEUE = "img_to_ghibli_queue";

    // 每次最多5个请求同时处理
    private static final String MAX_CONCURRENT_REQUESTS = "max_concurrent_requests";

    @Resource
    Img2TextApi img2TextApi;

    @Resource
    VipService vipService;

    @Resource
    Img2GhibliApi img2GhibliApi;

    @Resource
    LanguageTranslateApi languageTranslateApi;

    @Resource
    private ResourceLoader resourceLoader;

    @Resource
    private RedisService redisService;

    private final RBlockingQueue<ImgToGhibliRequest> requestQueue;
    private final ExecutorService executorService;
    private final Map<String, CompletableFuture<String>> pendingRequests = new ConcurrentHashMap<>();

    private RedissonClient redissonClient;

    public Img2TextService(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
        this.requestQueue = redissonClient.getBlockingQueue(IMG_TO_GHIBLI_QUEUE);
        this.executorService = new ThreadPoolExecutor(1, 1, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(5), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());

        processRequests();
    }


    /**
     * 调用图片反推提示词接口
     *
     * @param imageUrl 图片 URL
     * @return 生成的图片描述字符串
     */
    public String generateImageCaption(String imageUrl, String type, User user) {
        //校验url是否为piclumen站内的url
        UrlValidator.validateImageUrl(imageUrl);
        String caption = ""; // 初始化返回值
        try {
            // 构造请求参数
            String authorization = "Bearer YOUR_API_KEY";
            String prompt;
            if ("tag".equalsIgnoreCase(type)) {
                prompt = "Write a medium-length list of Booru tags for this image.";
            } else if ("des".equalsIgnoreCase(type)) {
                prompt = "Write a medium-length descriptive caption for this image in a casual tone.";
            } else {
                throw new BadRequestException("Invalid parameter");
            }
            CaptionRequest request = buildRequestBody(imageUrl, prompt, type);

            // 调用远程接口
            log.debug("调用图片描述生成接口，请求参数: {}", JsonUtils.writeToString(request));
            Response<CaptionResponse> response = img2TextApi.generateCaption(request);

            // 检查响应状态
            if (response.isSuccessful()) {
                CaptionResponse captionResponse = response.body();
                if (captionResponse != null && captionResponse.getChoices() != null && !captionResponse.getChoices().isEmpty()) {
                    // 提取第一个选项的内容
                    CaptionResChoice choice = captionResponse.getChoices().get(0);
                    if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                        caption = choice.getMessage().getContent(); // 获取生成的描述

                        //对lumen进行扣点
                        vipService.dealUserLumensCost(1, "", 0, user, LumenChangeSourceEnum.TOOLS.getValue(), "Describe Image", null);

                        log.info("图片描述生成成功，结果: {}", caption);
                    } else {
                        log.error("图片描述生成失败，响应体中未包含有效内容");
                    }
                } else {
                    log.error("图片描述生成失败，响应体为空或 choices 列表为空");
                }
            } else {
                log.error("图片描述生成接口调用失败，HTTP 状态码: {}, 错误信息: {}", response.code(), response.message());
            }
        } catch (IOException e) {
            log.error("调用图片描述生成接口时发生异常", e);
        }

        return caption; // 返回生成的描述
    }


    public String translateToLanguage(TranslateParams translateParams, User user) {
        String caption = ""; // 初始化返回值
        String content = translateParams.getContent();
        try {
            String resultLanguageType = LanguageCode.getValueByLabel(translateParams.getLanguageType());

            // 构造请求参数
            String authorization = "Bearer YOUR_API_KEY";
            String prompt = "";

            if (StringUtils.isNotBlank(translateParams.getMode())) {
                if ("translation".equals(translateParams.getMode())) {
                    prompt = "The input language is variation, you are a professional English translator, "
                            + "You must translate the entered words or sentences into English. "
                            + "I only need the translation result, no need for some prefix description. "
                            + "Take a deep breath, calm down, and start translating.";
                } else {
                    prompt = "The input language is variation, you are creating an English prompt for Stable "
                            + "Diffusion to generate an image. Step 1: understand the input and generate a text "
                            + "prompt for the input. Step 2: only respond in English with the prompt itself in phrase, "
                            + "but embellish it as needed but keep it under 200 tokens. Step 3: Just generate the final "
                            + "English prompt without any explanation and prefix or suffix, and without double quotes.";
                }

            } else if (StringUtils.isNotBlank(resultLanguageType)) {
                prompt = "The user input is English, You are a global language translation expert, "
                        + "Please translate the input into " + resultLanguageType
                        + " completely. I only need the translation result,"
                        + " no need for some prefix description. Take a deep breath, calm down, and start translating.";
            } else {
                throw new BadRequestException("Invalid parameter");
            }

            CaptionRequest request = buildTranslateRequestBody(prompt, content);

            // 调用远程接口
            log.debug("调用图片描述生成接口，请求参数: {}", JsonUtils.writeToString(request));
            Response<CaptionResponse> response = languageTranslateApi.languageTranslate(request);

            // 检查响应状态
            if (response.isSuccessful()) {
                CaptionResponse captionResponse = response.body();
                if (captionResponse != null && captionResponse.getChoices() != null && !captionResponse.getChoices().isEmpty()) {
                    // 提取第一个选项的内容
                    CaptionResChoice choice = captionResponse.getChoices().get(0);
                    if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                        caption = choice.getMessage().getContent(); // 获取生成的描述
                        log.info("图片描述生成成功，结果: {}", caption);
                    } else {
                        log.error("图片描述生成失败，响应体中未包含有效内容");
                    }
                } else {
                    log.error("图片描述生成失败，响应体为空或 choices 列表为空");
                }
            } else {
                log.error("图片描述生成接口调用失败，HTTP 状态码: {}, 错误信息: {}", response.code(), response.message());
            }
        } catch (IOException e) {
            log.error("调用图片描述生成接口时发生异常", e);
        }

        return caption; // 返回生成的描述
    }


    /**
     * 动态生成请求体
     *
     * @param content            翻译内容
     * @param prompt             提示词
     * @param resultLanguageType 翻译目标语言
     * @return JSON 格式的请求体
     */
    private CaptionRequest buildTranslateRequestBody(String prompt, String content) throws JsonProcessingException {
        // 定义 JSON 模板
        String template = loadTranslateTemplate();

        // 准备数据
        Map<String, String> data = new HashMap<>();
        data.put("prompt", prompt);
        data.put("content", content);

        // 使用 Mustache 渲染模板
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(template), "template");
        StringWriter writer = new StringWriter();
        try {
            mustache.execute(writer, data).flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 构造主请求对象
        CaptionRequest request = new CaptionRequest();
        request.setModel("Meta-Llama-3.1-8B-Instruct-bnb-4bit");
        request.setTemperature(0.6);
        request.setTopP(0.9);
        request.setMax_tokens(1024);
        List<Map<String, Object>> messages = JsonUtils.fromString(writer.toString(), List.class);
        request.setMessages(messages);

        return request;
    }

    /**
     * 动态生成请求体
     *
     * @param imageUrl 图片 URL
     * @param prompt   提示词
     * @return JSON 格式的请求体
     */
    private CaptionRequest buildRequestBody(String imageUrl, String prompt, String type) throws JsonProcessingException {
        // 定义 JSON 模板
        String template = loadTemplate();

        // 准备数据
        Map<String, String> data = new HashMap<>();
        data.put("systemContent", "You are a helpful image captioner.");
        data.put("textPrompt", prompt);
        data.put("imageUrl", imageUrl);

        // 使用 Mustache 渲染模板
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(template), "template");
        StringWriter writer = new StringWriter();
        try {
            mustache.execute(writer, data).flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 构造主请求对象
        CaptionRequest request = new CaptionRequest();
        request.setModel("llama-joycaption-alpha-two-hf-llava");
        request.setTemperature(0.8);
        List<Map<String, Object>> messages = JsonUtils.fromString(writer.toString(), List.class);
        request.setMessages(messages);

        if ("tag".equalsIgnoreCase(type)) {
            request.setTopP(0.7);
            request.setMax_tokens(256);
        } else {
            request.setTopP(0.8);
            request.setMax_tokens(1024);
        }
        return request;
    }

    /**
     * 加载模板文件内容
     *
     * @return 模板文件内容
     * @throws Exception 如果文件读取失败
     */
    private String loadTemplate() {
        try {
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:workflow/img2text-template.json");
            try (InputStream inputStream = resource.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                return reader.lines().collect(Collectors.joining("\n"));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String loadTranslateTemplate() {
        try {
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:workflow/translate-template.json");
            try (InputStream inputStream = resource.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                return reader.lines().collect(Collectors.joining("\n"));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public String imgToGhibli(String imageUrl, User user) {
        int ghibliQueueNums = (int) Optional.ofNullable(redisService.get(MAX_CONCURRENT_REQUESTS)).orElse(0);
        if (ghibliQueueNums == 0) {
            redisService.set(MAX_CONCURRENT_REQUESTS, 2);
            ghibliQueueNums = 2;
        }

        if (requestQueue.size() >= ghibliQueueNums) {
            throw new LogicDoException("The request is too frequent, please try again later!");
        }

        ImgToGhibliRequest request = new ImgToGhibliRequest(imageUrl, user);
        CompletableFuture<String> future = new CompletableFuture<>();
        pendingRequests.put(request.getImage_url(), future);

        try {
            requestQueue.put(request); // 可能会阻塞
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            log.error("Error while processing request", e);
            return null;
        } finally {
            pendingRequests.remove(request.getImage_url());
        }
    }

    private void processRequests() {
        executorService.submit(() -> {
            while (true) {
                try {
                    ImgToGhibliRequest request = requestQueue.take();

                    if (Objects.isNull(request) || StringUtil.isBlank(request.getImage_url())) {
                        return;
                    }

                    String lockKey = "lock:img2ghibli::" + request.getImage_url();
                    String taskId = UUID.randomUUID().toString();
                    boolean isLocked = redisService.tryLock(lockKey, taskId, 5, TimeUnit.SECONDS);

                    try {
                        if (isLocked) {
                            String caption = imgToGhibliResult(request.getImage_url(), request.getUser());
                            CompletableFuture<String> future = pendingRequests.remove(request.getImage_url());
                            if (future != null) {
                                future.complete(caption);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error processing request", e);
                    } finally {
                        // 释放锁
                        if (isLocked) {
                            redisService.unlock(lockKey, taskId);
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Error while processing requests", e);
                }
            }
        });
    }

    public String imgToGhibliResult(String imageUrl, User user) {
        try {
            Img2GhibliRequest request = new Img2GhibliRequest();
            request.setImage_url(imageUrl);
            log.debug("调用图片描述生成接口，请求参数: {}", JsonUtils.writeToString(request));
            Call<Map<String, Object>> call = img2GhibliApi.generateCaption(request);

            Response<Map<String, Object>> response = call.execute();
            if (response.isSuccessful() && (response.body() != null) && ((int) response.body().get("status") == 0)) {
                //对lumen进行扣点
                vipService.dealUserLumensCost(10, "", 0, user, LumenChangeSourceEnum.TOOLS.getValue(), "Describe Image", null);
                return (String) response.body().get("data");
            } else {
                throw new BadRequestException("Failed. Please try again later !");
            }
        } catch (Exception e) {
            log.error("API call failed", e);
            return null;
        }
    }
}
