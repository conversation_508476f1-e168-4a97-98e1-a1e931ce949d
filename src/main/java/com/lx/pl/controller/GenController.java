package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.annotation.NoToken;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.controller.flux.FluxController;
import com.lx.pl.controller.gemini.GeminiController;
import com.lx.pl.controller.midjourney.MidjourneyController;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.*;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.service.*;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.UrlValidator;
import com.lx.pl.util.VideoLumenUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

import static com.lx.pl.enums.LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT;


@Slf4j
@Tag(name = "图像生成接口")
@RestController
@RequestMapping("/api/gen")
public class GenController {

    @Value("${realistic.ModelId}")
    String realisticModelId;

    @Value("${anime.ModelId}")
    String animeModelId;

    @Value("${animeV3.ModelId}")
    String animeV3ModelId;

    @Value("${lineart.ModelId}")
    String lineartModelId;

    @Value("${pony.ModelId}")
    String ponyModelId;

    @Value("${art.ModelId}")
    String artModelId;
    @Value("${ttapimj.modelId}")
    String ttapiMjModelId;
    @Value("${fluxKontextPro.modelId}")
    String fluxKontextProModelId;
    @Value("${geminiNanoBanana.modelId}")
    String geminiNanoBananaModelId;

    @Value("${picflow.modelId}")
    String picflowModelId;

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Autowired
    GenService genService;

    @Autowired
    PromptFiltrationService promptFiltrationService;

    @Autowired
    VipService vipStandardsService;

    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;
    @Resource
    private LumenService lumenService;

    @Autowired
    private MidjourneyController midjourneyController;
    @Autowired
    private FluxController fluxController;

    @Autowired
    VideoService videoService;
    @Autowired
    private GeminiController geminiController;
    @Autowired
    private ModelService modelService;
    @Autowired
    private FluxService fluxService;

    @Operation(summary = "通用生图，附加功能（按优先级排序）：1.去背景 2.Hires.fix")
    @PostMapping("/prompt")
    @Authorization
    @Deprecated
    public R<Map<String, String>> genGeneric(@RequestBody GenGenericPara genParameters,
                                             @CurrentUser @Parameter(hidden = true) User user)
            throws IOException {
        if (null == genParameters.getPrompt()) {
            genParameters.setPrompt("");
        }
        if (null == genParameters.getNegative_prompt()) {
            genParameters.setNegative_prompt("");
        }
        if (null == genParameters.getResolution()) {
            return R.fail(400, "Image dimensions required !");
        }
        if (0 >= genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width required !");
        }
        if (0 >= genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height required !");
        }
        if (2048 < genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width exceeds 3000 pixels !");
        }
        if (2048 < genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height exceeds 3000 pixels !");
        }
        if (1 > genParameters.getResolution().getBatch_size()) {
            return R.fail(400, "Unfinished tasks pending. Do not repeat !");
        }
        String promptId = genService.prompt(genParameters, user);
        Map<String, String> m = new HashMap<>();
        m.put("promptId", promptId);
        return R.success(m);
    }

    @Operation(summary = "单个任务完成情况查询")
    @GetMapping("/history/{promptId}")
    @Authorization
    @Deprecated
    public R<List<Image>> history(@PathVariable("promptId") String promptId,
                                  @CurrentUser @Parameter(hidden = true) User user) {
        List<Image> history = genService.history(promptId, user);
        return R.success(history);
    }

//    @Operation(summary = "图像处理入库")
//    @PostMapping("/forge")
//    @Authorization
//    @Deprecated
//    public R<List<Forge>> forge(@RequestBody ForgeParameters para,
//                                @CurrentUser @Parameter(hidden = true) User user) {
//        return R.success(genService.forge(para, user));
//    }

    @Operation(summary = "获取一个随机提示词")
    @GetMapping("/random-prompt")
    @Authorization
    public R<String> randomPrompt() {
        return R.success(genService.randomPrompt());
    }

    @Operation(summary = "获取模型列表")
    @GetMapping("/model/list")
    @Authorization
    public R<List<ModelInformation.ModelAbout>> listModels(HttpServletRequest request, @CurrentUser @Parameter(hidden = true) User user) throws IOException {
        return R.success(modelService.listModels(request.getHeader("Platform"), user));
    }

    @Operation(summary = "图片生成")
    @PostMapping("/create")
    @Authorization
    public R<Map<String, Object>> create(@RequestBody GenGenericPara genParameters,
                                         @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {
        //如果模型为mj，则调用mj接口
        if (genParameters.getModel_id().equals(ttapiMjModelId)) {
            return midjourneyController.createCompatible(user, genParameters, request);
        }
        //如果模型为flux kontext pro，则调用flux接口
        if (genParameters.getModel_id().equals(fluxKontextProModelId)) {
            return fluxController.createCompatible(user, genParameters, request);
        }
        //如果是视频生成模型，则调用视频生成接口
        if (genParameters.getModel_id().equals(picflowModelId)) {
            return videoGeneration(genParameters, user, request);
        }
        //如果模型为gemini nano banana，则调用gemini接口
        if (genParameters.getModel_id().equals(geminiNanoBananaModelId)) {
            return geminiController.createCompatible(user, genParameters, request);
        }
        if (null == genParameters.getPrompt()) {
            genParameters.setPrompt("");
        }
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        if (null == genParameters.getNegative_prompt()) {
            genParameters.setNegative_prompt("");
        }
        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        if (null == genParameters.getResolution()) {
            return R.fail(400, "Image dimensions required !");
        }
        if (0 >= genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width required !");
        }
        if (0 >= genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height required !");
        }
        if (2048 < genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width exceeds 2048 pixels !");
        }
        if (2048 < genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height exceeds 2048 pixels !");
        }
        if (1 > genParameters.getResolution().getBatch_size()) {
            return R.fail(400, "At least one image required !");
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        Boolean paramHighPixels = !Objects.isNull(genParameters.getHighPixels()) ? genParameters.getHighPixels() : Boolean.FALSE;
        //限定为art模型
        Boolean highPixels = paramHighPixels && artModelId.equals(genParameters.getModel_id());

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());
        //普通用户生图不能超过两张
        if (notVip && (genParameters.getResolution().getBatch_size() > 2)) {
            return R.fail(400, "Images_per_batch of Basic cannot exceed 2 !");
        }

        //判断是否fast模式
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user,
                genParameters.getResolution().getBatch_size(), highPixels, OriginCreate.create, null, null);

        //如果是dev模型或者是超200万的高清图片，则必须是能扣点的快速生图
        if (genService.checkDevModel(genParameters.getModel_id()) || highPixels) {
            //普通用户,或者会员不足点数不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }
        //如果是krea模型,则必须是能扣点的快速生图
        if (genService.checkKreaModel(genParameters.getModel_id())) {
            //不足点数不支持
            if (!fastHour) {
                throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            if (modelRightsVerifyResult.isUsePayTrial()) {
                throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
            }
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), genParameters.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(genParameters.getResolution().getBatch_size(), request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setHighPixels(highPixels);

        return R.success(genService.create(genParameters, createPicParams, user, modelRightsVerifyResult));
    }


    @Operation(summary = "图片结果回调")
    @PostMapping("/call-back")
    @Authorization
    @NoToken
    public R<Boolean> callBack(@RequestBody R<BackendCallBackParams> callBackParam)
            throws IOException {
        Boolean result = genService.callBack(callBackParam);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户未完成任务查询")
    @PostMapping("/not-finish-task")
    @Authorization
    public R<List<GenGenericResp>> notFinishTask(@CurrentUser @Parameter(hidden = true) User user)
            throws IOException {
        return R.success(lumenService.notFinishTask(user.getLoginName()));
    }

    @Operation(summary = "用户未完成任务查询")
    @PostMapping("/task-queue")
    @Authorization
    public R<NotFinishTaskResult> getNotFinishTaskResult(@CurrentUser @Parameter(hidden = true) User user) throws IOException {
        return R.success(genService.getNotFinishTaskResult(user));
    }

    @Operation(summary = "图片去背景")
    @PostMapping("/remove-background")
    @Authorization
    public R<Map<String, Object>> rmBackgroundPicture(
            @RequestBody DealImgParams dealImgParams,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws IOException {
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(dealImgParams.getModelId(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());

        //判断是否fast模式
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, 1, Boolean.FALSE, OriginCreate.removeBackground,
                dealImgParams.getImgUrl(), null);
        if (!fastHour && notVip) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }
        //如果是dev模型，则必须是vip，且是能扣点的快速生图
        if (genService.checkDevModel(dealImgParams.getModelId())) {
            //普通用户暂时不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), dealImgParams.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(1, request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());


        return R.success(genService.rmBackgroundPicture(dealImgParams, createPicParams, user));
    }

    @Operation(summary = "图片高清修复")
    @PostMapping("/hires-fix")
    @Authorization
    public R<Map<String, Object>> hiresFixPicture(
            @RequestBody DealImgParams dealImgParams,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws IOException {
        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(dealImgParams.getModelId(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //判断是否fast模式
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, 1, Boolean.FALSE, OriginCreate.hiresFix,
                dealImgParams.getImgUrl(), dealImgParams.getScale());
        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());
        if (!fastHour && notVip) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        //如果是dev模型，则必须是vip，且是能扣点的快速生图
        if (genService.checkDevModel(dealImgParams.getModelId())) {
            //普通用户,或者会员不足点数不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }
        //如果是ttapiMj模型，则必须是能扣点的快速生图
        if (genService.checkTtapiMjModel(dealImgParams.getModelId())) {
            //普通用户,或者会员不足点数不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), dealImgParams.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(1, request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());

        return R.success(genService.hiresFixPicture(dealImgParams, createPicParams, user));
    }

    @Operation(summary = "局部重绘")
    @PostMapping(value = "/local-redraw")
    @Authorization
    public R<Map<String, Object>> localRedraw(@RequestParam("genParameters") String genParameters,
                                              @RequestParam("redrawImg") MultipartFile redrawImg,
                                              @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {
        GenGenericPara genGenericPara = JsonUtils.fromString(genParameters, GenGenericPara.class);
        //如果是mj模型，暂不支持
        if (genService.checkTtapiMjModel(genGenericPara.getModel_id())) {
            throw new LogicException(LogicErrorCode.MODEL_NOT_SUPPORT);
        }

        if (StringUtil.isBlank(genParameters)) {
            return R.fail(400, "Parameter cannot be empty !");
        }
        if (genGenericPara.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        if (null == genGenericPara.getGenLocalRedrawPara().getImg_url()) {
            return R.fail(400, "Image url required !");
        }

        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters);
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genGenericPara.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genGenericPara.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //判断是否fast模式
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, 1, Boolean.FALSE, OriginCreate.localRedraw,
                genGenericPara.getGenLocalRedrawPara().getImg_url(), null);
        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());
        if (!fastHour && notVip) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        //如果是dev模型，则必须是vip，且是能扣点的快速生图
        if (genService.checkDevModel(genGenericPara.getModel_id())) {
            //普通用户暂时不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), genGenericPara.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(1, request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());

        return R.success(genService.localRedraw(genGenericPara, redrawImg, createPicParams, user));
    }

    @Operation(summary = "扩图")
    @PostMapping(value = "/enlarge-image")
    @Authorization
    public R<Map<String, Object>> enlargeImage(@RequestBody GenGenericPara genParameters,
                                               @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {
        //如果是mj模型，暂不支持
        if (genService.checkTtapiMjModel(genParameters.getModel_id())) {
            throw new LogicException(LogicErrorCode.MODEL_NOT_SUPPORT);
        }

        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        if (null == genParameters.getEnlargeImagePara().getImg_url()) {
            return R.fail(400, "Image url required !");
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //判断是否fast模式
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, 1, Boolean.FALSE,
                OriginCreate.enlargeImage, genParameters.getEnlargeImagePara().getImg_url(), null);
        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());
        if (!fastHour && notVip) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        //如果是dev模型，则必须是vip，且是能扣点的快速生图
        if (genService.checkDevModel(genParameters.getModel_id())) {
            //普通用户暂时不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), genParameters.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(1, request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());

        return R.success(genService.enlargeImage(genParameters, createPicParams, user));
    }

    @Operation(summary = "线稿上色")
    @PostMapping(value = "/lineRecolor")
    @Authorization
    public R<Map<String, Object>> lineRecolor(@RequestBody GenGenericPara genParameters,
                                              @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {
        //如果是mj模型，暂不支持
        if (genService.checkTtapiMjModel(genParameters.getModel_id())) {
            throw new LogicException(LogicErrorCode.MODEL_NOT_SUPPORT);
        }

        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }

        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        if (null == genParameters.getLineRecolorPara().getImg_url()) {
            return R.fail(400, "Image url required !");
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //判断是否是fast生图
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, 1, Boolean.FALSE, OriginCreate.lineRecolor,
                genParameters.getLineRecolorPara().getImg_url(), null);

        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());
        if (!fastHour && notVip) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        //如果是dev模型，则必须是vip，且是能扣点的快速生图
        if (genService.checkDevModel(genParameters.getModel_id())) {
            //普通用户暂时不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), genParameters.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(1, request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());

        return R.success(genService.lineRecolor(genParameters, createPicParams, user));
    }

//    @Operation(summary = "图片微变")
//    @PostMapping(value = "/vary-image")
//    @Authorization
//    public R<Map<String, Object>> varyImage(@RequestBody GenGenericPara genParameters,
//                                            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
//            throws IOException {
//        //普通用户暂时不支持
//        if(VipType.basic.getValue().equals(user.getVipType())){
//            return R.fail(4001, "Please recharge VIP membership first !");
//        }
//
//        if (StringUtils.isNotBlank(genParameters.getPrompt()) && genParameters.getPrompt().length() > 3000) {
//            return R.fail(400, "Image prompt exceeds 3000!");
//        }
//        if (null == genParameters.getVaryPara().getImg_url()) {
//            return R.fail(400, "Image url required !");
//        }
//        if (!(animeModelId.equals(genParameters.getModel_id()) || realisticModelId.equals(genParameters.getModel_id())
//                || ponyModelId.equals(genParameters.getModel_id()))) {
//            return R.fail(400, "Model not supported !");
//        }
//        if (!genService.checkUserPrompt(user)) {
//            return R.fail(400, "Unfinished tasks pending. Do not repeat !");
//        }
//
//        String platform = genService.getPlatform(request);
//        if (StringUtil.isBlank(platform)) {
//            return R.fail(400, "Illegal request !");
//        }
//
//        //判断是否是特殊规则
//        Boolean specialFlag = vipStandardsService.judgeUserVipFastCreate(genParameters.getModel_id(), user, 1);
//
//        return R.success(genService.varyImage(genParameters, specialFlag, platform, user));
//    }

    @Operation(summary = "局部重绘(new)")
    @PostMapping(value = "/inpaint")
    @Authorization
    public R<Map<String, Object>> inpaint(@RequestBody GenGenericPara genGenericPara,
                                          @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {
        //如果是mj模型，暂不支持
        if (genService.checkTtapiMjModel(genGenericPara.getModel_id())) {
            throw new LogicException(LogicErrorCode.MODEL_NOT_SUPPORT);
        }

        if (genGenericPara.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        if (null == genGenericPara.getGenLocalRedrawPara().getImg_url()) {
            return R.fail(400, "Image url required !");
        }

        Boolean filterChildSex = promptFiltrationService.filterChildSex(genGenericPara.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genGenericPara.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genGenericPara.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //判断是否fast模式
        Boolean fastHour = vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, 1, Boolean.FALSE, OriginCreate.localRedraw,
                genGenericPara.getGenLocalRedrawPara().getImg_url(), null);
        //判断是否是普通用户
        boolean notVip = VipType.basic.getValue().equals(modelRightsVerifyResult.getVipType());
        if (!fastHour && notVip) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        //如果是dev模型，则必须是vip，且是能扣点的快速生图
        if (genService.checkDevModel(genGenericPara.getModel_id())) {
            //普通用户暂时不支持
            if (!fastHour) {
                throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        //判断是否是relax生图
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTime(user.getLoginName(), genGenericPara.getContinueCreate(), notVip, request);
            genService.addRelaxCreateImgNumByIp(1, request);
        }

        //设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());

        return R.success(genService.inpaint(genGenericPara, createPicParams, user));
    }

    @Operation(summary = "图片编辑")
    @PostMapping("/edit")
    @Authorization
    public R<Map<String, Object>> edit(@RequestBody GenGenericPara genParameters,
                                       @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {

        //模型校验，目前只开放flux模型可编辑图片
        if (!genService.checkEditModel(genParameters.getModel_id())) {
            return R.fail(400, "Model not support !");
        }

        //校验url是否为piclumen站内的url
        if (!UrlValidator.isValidImageUrl(genParameters.getImgEditPara().getImgUrl())) {
            return R.fail(400, "Image not support !");
        }

        // 基础参数验证
        if (genParameters.getPrompt() == null || genParameters.getPrompt().isEmpty()) {
            throw new LogicException(LogicErrorCode.FLUX_PROMPT_REQUIRED);
        }

        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }

        if (genParameters.getImgEditPara() == null || StringUtils.isBlank(genParameters.getImgEditPara().getImgUrl())) {
            return R.fail(400, "Illegal request !");
        }

        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        //设置生图张数固定为1张
        genParameters.getResolution().setBatch_size(1);

        Boolean fastHour = true;
        //校验点数
        if (!vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, genParameters.getResolution().getBatch_size(), Boolean.FALSE, OriginCreate.create, null, null)) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 设置传参
        String markId = LogicConstants.FLUX_MARKID_PREFIX + UUID.randomUUID();
        if (genService.checkUserConcurrentJobs(user, markId, false)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查Flux并发任务数限制
        if (fluxService.checkFluxConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.FLUX_EXCEED_CONCURRENT_JOBS);
        }

        // 判断生图类型
        String feature = FeaturesType.edit.getValue();

        // 调用Flux API
        FluxResponse.CreateTaskResponse fluxResult = fluxService.editKontextProTask(
                genParameters.getPrompt(),
                user,
                markId,
                fastHour,
                platform,
                genParameters,
                feature,
                modelRightsVerifyResult
        );

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("markId", markId);
        result.put("index", 0);
        result.put("fastHour", fastHour);
        result.put("featureName", feature);

        return R.success(result);
    }

    @Operation(summary = "多图片编辑")
    @PostMapping("/multi-edit")
    @Authorization
    public R<Map<String, Object>> multiEdit(@RequestBody GenGenericPara genParameters,
                                            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {

        //模型校验，目前只开放flux模型可编辑图片
        if (!genService.checkEditModel(genParameters.getModel_id())) {
            return R.fail(400, "Model not support !");
        }

        if (genParameters.getMultiImgEditPara() == null || genParameters.getMultiImgEditPara().isEmpty()) {
            return R.fail(400, "Illegal request !");
        }
        if (genParameters.getMultiImgEditPara().get(0).getImgUrl() == null) {
            return R.fail(400, "Illegal request !");
        }

        //校验url是否为piclumen站内的url
        genParameters.getMultiImgEditPara().forEach(imgEditPara -> {
            if (!UrlValidator.isValidImageUrl(imgEditPara.getImgUrl())) {
                throw new LogicException(LogicErrorCode.IMAGE_NOT_SUPPORT);
            }
        });

        // 基础参数验证
        if (genParameters.getPrompt() == null || genParameters.getPrompt().isEmpty()) {
            throw new LogicException(LogicErrorCode.FLUX_PROMPT_REQUIRED);
        }

        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }

        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support !");
        }

        //模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        if (!modelRightsVerifyResult.isPassVerify()) {
            throw new LogicException(NOT_ENOUGH_LUMENS_SUPPORT);
        }

        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        //是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                return R.fail(400, "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.");
            }
        }

        //设置生图张数固定为1张
        genParameters.getResolution().setBatch_size(1);

        // 判断是否是普通用户
//        Boolean notVip = VipType.basic.getValue().equals(user.getVipType());

        Boolean fastHour = true;

//        //检查权限
//        if (notVip) {
//            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
//        }

        //校验点数
        if (!vipStandardsService.judgeUserFastCreate(modelRightsVerifyResult, user, genParameters.getResolution().getBatch_size(), Boolean.FALSE, OriginCreate.create, null, null)) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 设置传参
        String markId = LogicConstants.FLUX_MARKID_PREFIX + UUID.randomUUID();
        if (genService.checkUserConcurrentJobs(user, markId, false)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查Flux并发任务数限制
        if (fluxService.checkFluxConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.FLUX_EXCEED_CONCURRENT_JOBS);
        }

        // 判断生图类型
        String feature = FeaturesType.edit.getValue();

        // 调用Flux API
        FluxResponse.CreateTaskResponse fluxResult = fluxService.editMultiKontextProTask(
                genParameters.getPrompt(),
                user,
                markId,
                fastHour,
                platform,
                genParameters,
                feature,
                modelRightsVerifyResult
        );

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("markId", markId);
        result.put("index", 0);
        result.put("fastHour", fastHour);
        result.put("featureName", feature);

        return R.success(result);
    }

    @Operation(summary = "视频生成")
    @PostMapping("/video-generation")
    @Authorization
    public R<Map<String, Object>> videoGeneration(@RequestBody GenGenericPara genParameters,
                                                  @CurrentUser @Parameter(hidden = true) User user,
                                                  HttpServletRequest request) throws IOException {

        if (genParameters.getVideoGenerationPara() == null) {
            return R.fail(400, "Video generation parameters required!");
        }

        if (null == genParameters.getNegative_prompt()) {
            genParameters.setNegative_prompt("");
        }

        GenGenericPara.VideoGenerationPara videoPara = genParameters.getVideoGenerationPara();

        // 验证video_type
        if (videoPara.getVideo_type() == null ||
                (!videoPara.getVideo_type().equals("text_to_video") && !videoPara.getVideo_type().equals("image_to_video"))) {
            return R.fail(400, "Invalid video_type!");
        }

        // 图生视频需要img_url
        if ("image_to_video".equals(videoPara.getVideo_type())) {
            if (videoPara.getImgs() == null || videoPara.getImgs().isEmpty()) {
                return R.fail(400, "img_url list is required for image_to_video!");
            }

            // 验证每个图片URL
            for (GenGenericPara.PositionImg positionImg : videoPara.getImgs()) {
                if (positionImg.getImg_url() == null || positionImg.getImg_url().trim().isEmpty()) {
                    return R.fail(400, "Each img_url in the list cannot be empty!");
                }
                if (!UrlValidator.isValidImageUrl(positionImg.getImg_url())) {
                    return R.fail(400, "Invalid img_url: " + positionImg.getImg_url());
                }
            }
        }

        // 视频长度目前只支持3s
        if (videoPara.getDuration() != null && videoPara.getDuration() != 3) {
            return R.fail(400, "Invalid video duration!");
        } else {
            videoPara.setDuration(3);
        }

        // 文生视频需要width和height
        if ("text_to_video".equals(videoPara.getVideo_type())) {
            if (null == genParameters.getResolution()) {
                return R.fail(400, "Image dimensions required !");
            }
            if (0 >= genParameters.getResolution().getWidth()) {
                return R.fail(400, "Image width required !");
            }
            if (0 >= genParameters.getResolution().getHeight()) {
                return R.fail(400, "Image height required !");
            }
            if (2048 < genParameters.getResolution().getWidth()) {
                return R.fail(400, "Image width exceeds 2048 pixels !");
            }
            if (2048 < genParameters.getResolution().getHeight()) {
                return R.fail(400, "Image height exceeds 2048 pixels !");
            }
            //只允许640*480，720*480，1280*720，768*768，720*1280，480*720，480*640这些比例
            if (!VideoLumenUtil.isSupportedTextToVideoSize(genParameters.getResolution().getWidth(), genParameters.getResolution().getHeight())) {
                return R.fail(400, "Unsupported video size !");
            }
        }

        // Prompt长度验证
        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Video prompt exceeds 3000 characters!");
        }

        // 内容过滤
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        // 获取平台信息
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request!");
        }

        if (!videoService.isVideoModel(genParameters.getModel_id())) {
            return R.fail(400, "Model not support!");
        }

        // 模型验证
        ModelInformation.ModelAbout modelAbout = genService.checkPlatformModel(genParameters.getModel_id(), request);
        if (modelAbout == null) {
            return R.fail(400, "Model not support!");
        }

        // 模型权益校验
        ModelRightsVerifyResult modelRightsVerifyResult = modelService.checkModelRights(modelAbout, user);
        modelRightsVerifyResult.checkPassVerify();

        // 判断是否是pro用户
        boolean isPro = VipType.pro.getValue().equals(modelRightsVerifyResult.getVipType());

        // 判断是否fast模式 - 使用视频专用方法
        OriginCreate originCreate = "text_to_video".equals(videoPara.getVideo_type()) ? OriginCreate.textToVideo : OriginCreate.imageToVideo;
        Boolean fastHour = vipStandardsService.judgeUserFastCreateForVideo(modelRightsVerifyResult, user, originCreate,
                videoPara.getVideo_type(), genParameters.getResolution());

        // 如果是picflow模型，则必须是能扣点的快速生图或者pro用户
        if (genService.checkPicflowModel(genParameters.getModel_id()) && !isPro) {
            if (!fastHour) {
                throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
            }
        }

        int relaxWaitTime = 0;
        // 判断是否是relax生成视频
        if (!fastHour) {
            relaxWaitTime = genService.getRelaxWaitTimeForVideo(user.getLoginName());

            log.info("traceId:{}, relaxWaitTime:{}", MDC.get("TRACE_ID"), relaxWaitTime);
        }

        // 设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        CreatePicParams createPicParams = new CreatePicParams();
        createPicParams.setFastHour(fastHour);
        createPicParams.setPlatform(platform);
        createPicParams.setMarkId(markId);
        createPicParams.setRelaxWaitTime(relaxWaitTime);
        createPicParams.setCostLumens(modelRightsVerifyResult.getCostLumen());

        return R.success(videoService.generateVideo(genParameters, createPicParams, user, modelRightsVerifyResult));
    }
}
