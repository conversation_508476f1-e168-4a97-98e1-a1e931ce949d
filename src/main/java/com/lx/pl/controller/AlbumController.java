package com.lx.pl.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.AlbumParams;
import com.lx.pl.dto.AlbumResult;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.AlbumService;
import com.lx.pl.util.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Objects;

@Slf4j
@Tag(name = "用户相册接口")
@RestController
@RequestMapping("/api/album")
public class AlbumController {

    @Value("${logic.album.maxImgNums}")
    Integer albumMaxImgNums;

    @Autowired
    AlbumService albumService;

    @Operation(summary = "上传用户图片到相册")
    @PostMapping(path = "/upload-album-img")
    @Authorization
    public R<AlbumResult> uploadAlbumImg(@RequestParam("albumImg") MultipartFile albumImg,
                                     @RequestParam("albumParams") String albumParams,
                                     @Parameter(hidden = true) @CurrentUser User user) throws IOException {
        if (user.getAlbumImgNum() + 1 > albumMaxImgNums) {
            return R.fail(400, "You have reached the maximum number of uploads!");
        }
        AlbumParams params = JsonUtils.fromString(albumParams, AlbumParams.class);
        AlbumResult result = albumService.uploadAlbumImg(albumImg,params, user);

        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Image upload failed, please select another image !");
    }

    @Operation(summary = "上传用户图片到相册")
    @PostMapping(path = "/upload-album-img-v2")
    @Authorization
    public R<AlbumResult> uploadAlbumImgV2(@RequestBody AlbumParams albumParams,
                                           @Parameter(hidden = true) @CurrentUser User user) throws IOException {
        if (user.getAlbumImgNum() + 1 > albumMaxImgNums) {
            return R.fail(400, "You have reached the maximum number of uploads!");
        }
        AlbumResult result = albumService.uploadAlbumImgV2(albumParams, user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Image upload failed, please select another image !");
    }

    @Operation(summary = "在相册中删除某张用户图片")
    @PostMapping("/delete-album-img")
    @Authorization
    public R<String> deleteAlbumImg(
            @RequestParam("id") @Parameter(description = "图片id") Long id,
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        boolean result = albumService.deleteAlbumImg(id, user);
        return result ? R.success("") : R.fail(1, "Failed");
    }

    @Operation(summary = "在相册查询当前用户上传的图片")
    @GetMapping("/album-img-list")
    @Authorization
    public R<PromptPageInfo<AlbumResult>> getAlbumImgList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(albumService.getAlbumImgList(pageNum, pageSize, user));
    }
}
