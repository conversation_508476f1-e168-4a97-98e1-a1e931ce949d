package com.lx.pl.controller.resources;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.resources.CommonResourcesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description
 */
@Tag(name = "资源管理相关接口")
@RestController
@RequestMapping("api/common-resources")
public class CommonResourcesController {
    @Resource
    private CommonResourcesService commonResourcesService;

    @Operation(summary = "获取所有可用的支付渠道")
    @GetMapping("/available-payment-channels")
    @Authorization
    public R<List<String>> availablePaymentChannels(HttpServletRequest request) {
        String platform = request.getHeader("Platform");
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Platform is required !");
        }
        return R.success(commonResourcesService.availablePaymentChannels(platform));
    }
}
