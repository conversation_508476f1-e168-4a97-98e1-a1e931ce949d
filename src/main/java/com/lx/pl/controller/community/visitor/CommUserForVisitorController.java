package com.lx.pl.controller.community.visitor;

import com.lx.pl.db.mysql.community.entity.CommUser;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/19
 * @description
 */
@Tag(name = "社区用户相关接口（游客模式）")
@Slf4j
@RestController
@RequestMapping("/api/comm-user-for-visitor")
public class CommUserForVisitorController {
    @Autowired
    private CommUserService commUserService;

    @Operation(summary = "查询社区用户信息")
    @PostMapping("/select-user")
    public R<CommUser> getCommUser(@RequestParam(value = "userId") @Parameter(description = "查询的用户id") Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("userId cannot be null");
        }
        return R.success(commUserService.queryCommunityUserInfo(userId));
    }
}
