package com.lx.pl.controller.community.visitor;

import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.ModelInformation;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommImgService;
import com.lx.pl.service.ModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9
 * @description
 */
@Tag(name = "社区图片相关接口（游客模式）")
@Slf4j
@RestController
@RequestMapping("/api/comm-img-for-visitor")
public class CommImgForVisitorController {
    @Autowired
    private CommImgService commImgService;
    @Autowired
    private ModelService modelService;

    @Operation(summary = "分页查询社区图片")
    @GetMapping("/page-search")
    public R<CommPageInfo<CommFile>> getCommImgByPage(@RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                      @RequestParam("pageSize") Integer pageSize,
                                                      @RequestParam(required = false) List<String> tags,
                                                      @RequestParam(value = "collationName", required = false)
                                                      @Parameter(description = "排序规则名称：Hot : 点赞数排序   Likes : 用户点赞  Featured : 精选的 New: 最新的")
                                                      String collationName,
                                                      @RequestParam(value = "vagueKey", required = false) String vagueKey) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getCommImgByPageForVisitor(lastFileId, false, null, pageSize, tags, collationName, vagueKey));
    }

    @Operation(summary = "获取模型列表")
    @GetMapping("/model/list")
    public R<List<ModelInformation.ModelAbout>> listModels(HttpServletRequest request) throws IOException {
        return R.success(modelService.listModels(request.getHeader("Platform"), null));
    }

    @Operation(summary = "分页查询社区图片v2")
    @GetMapping("/page-search-v2")
    public R<CommPageInfo<CommFile>> getCommImgByPageV2(@RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                        @RequestParam(value = "lastLikeId", required = false) String lastLikeId,
                                                        @RequestParam(value = "lastScoreIndex", required = false) Long lastScoreIndex,
                                                        @RequestParam("pageSize") Integer pageSize,
                                                        @RequestParam(required = false) List<String> tags,
                                                        @RequestParam(value = "collationName", required = false)
                                                        @Parameter(description = "排序规则名称：Hot : 点赞数排序   Likes : 用户点赞  Featured : 精选的  New: 最新的， Trending : 趋势")
                                                        String collationName,
                                                        @RequestParam(value = "vagueKey", required = false) String vagueKey) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getCommImgByPageForVisitor(lastFileId, true, lastScoreIndex, pageSize, tags, collationName, vagueKey));
    }
}
