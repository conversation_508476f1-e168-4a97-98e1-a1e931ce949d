package com.lx.pl.controller.community.visitor;

import com.lx.pl.db.mysql.community.entity.CommComment;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/9
 * @description
 */
@Tag(name = "社区评论相关接口（游客模式）")
@Slf4j
@RestController
@RequestMapping("/api/comm-comment-for-visitor")
public class CommCommentForVisitorController {
    @Autowired
    private CommCommentService commCommentService;

    @Operation(summary = "获取社区评论列表")
    @GetMapping("/select-comment")
    public R<CommPageInfo<CommComment>> getCommCommentList(@RequestParam(value = "lastCommentId", required = false) String lastCommentId,
                                                           @RequestParam("pageSize") Integer pageSize,
                                                           @RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                                           @RequestParam(value = "firstCommentId", required = false) @Parameter(description = "社区一级评论id") String firstCommentId) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commCommentService.getCommCommentListForVisitor(lastCommentId, pageSize, commFileId, firstCommentId));
    }
}
