package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.BannerImgIos;
import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.db.mysql.community.entity.CommFileDetail;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommImgBatch;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommImgService;
import com.lx.pl.service.CommonService;
import com.lx.pl.service.FileScoreService;
import com.lx.pl.util.AESUtil;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import com.lx.pl.vo.BannerImgIosVO;
import com.lx.pl.vo.BannerImgVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "社区图片相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-img")
public class CommImgController {

    @Autowired
    private CommImgService commImgService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private FileScoreService fileScoreService;

    @Operation(summary = "删除社区图片")
    @PostMapping("/delete")
    @Authorization
    public R<Boolean> deleteCommFileById(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commImgService.deleteCommFileById(commFileId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }


    @Operation(summary = "分享或者remix")
    @PostMapping("share-remix")
    @Authorization
    public R<Boolean> shareOrRemix(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                   @RequestParam("type") @Parameter(description = "操作类型：remix ,share ") String type,
                                   @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commImgService.shareOrRemix(commFileId, type, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "分页查询社区图片")
    @GetMapping("/page-search")
    @Authorization
    public R<CommPageInfo<CommFile>> getCommImgByPage(@RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                      @RequestParam(value = "lastLikeId", required = false) String lastLikeId,
                                                      @RequestParam("pageSize") Integer pageSize,
                                                      @RequestParam(required = false) List<String> tags,
                                                      @RequestParam(value = "collationName", required = false)
                                                      @Parameter(description = "排序规则名称：Hot : 点赞数排序   Likes : 用户点赞  Featured : 精选的 New: 最新的")
                                                      String collationName,
                                                      @RequestParam(value = "vagueKey", required = false) String vagueKey,
                                                      @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getCommImgByPage(lastFileId, lastLikeId, false, null, pageSize, tags, collationName, vagueKey, null, user));
    }

    @Operation(summary = "分页查询社区图片v2")
    @GetMapping("/page-search-v2")
    @Authorization
    public R<CommPageInfo<CommFile>> getCommImgByPageV2(@RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                        @RequestParam(value = "lastLikeId", required = false) String lastLikeId,
                                                        @RequestParam(value = "lastScoreIndex", required = false) Long lastScoreIndex,
                                                        @RequestParam("pageSize") Integer pageSize,
                                                        @RequestParam(required = false) List<String> tags,
                                                        @RequestParam(value = "collationName", required = false)
                                                        @Parameter(description = "排序规则名称：Hot : 点赞数排序   Likes : 用户点赞  Featured : 精选的  New: 最新的， Trending : 趋势")
                                                        String collationName,
                                                        @RequestParam(value = "vagueKey", required = false) String vagueKey,
                                                        @RequestParam(value = "fileType", required = false)
                                                        @Parameter(description = "文件类型 0-图片 1-视频") Integer fileType,
                                                        @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getCommImgByPage(lastFileId, lastLikeId, true, lastScoreIndex, pageSize, tags, collationName, vagueKey, fileType, user));
    }


    @Operation(summary = "分页查询社区图片（未登录）")
    @GetMapping("/common-page-search")
    public R<CommPageInfo<CommFile>> getCommImgByCommonPage(@RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                            @RequestParam("pageSize") Integer pageSize) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getCommImgByCommonPage(lastFileId, pageSize));
    }

    @Operation(summary = "个人主页社区图片")
    @GetMapping("/page-personal-search")
    @Authorization
    public R<CommPageInfo<CommFile>> getPersonalCommImgByPage(@RequestParam(value = "lastFileId", required = false) String lastFileId,
                                                              @RequestParam("pageSize") Integer pageSize,
                                                              @RequestParam(value = "vagueKey", required = false) String vagueKey,
                                                              @RequestParam(required = false) List<String> tags,
                                                              @RequestParam(value = "userId", required = false) @Parameter(description = "其他人的用户id") Long userId,
                                                              @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commImgService.getPersonalCommImgByPage(lastFileId, pageSize, userId, vagueKey, tags, user));
    }

    @Operation(summary = "查询社区图片详情")
    @GetMapping("/img-detail")
    public R<String> getCommFileDetailById(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId) throws Exception {
        CommFileDetail commFileDetail = commImgService.getCommFileDetailById(commFileId);
        return R.success(AESUtil.encryptString(JsonUtils.writeToString(commFileDetail)));
    }


    @Operation(summary = "查询社区用户图片详情")
    @GetMapping("/img-particular")
    @Authorization
    public R<String> getCommFileParticularById(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                               @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        CommFileDetail commFileDetail = commImgService.getCommFileParticularById(commFileId, user);
        return R.success(AESUtil.encryptString(JsonUtils.writeToString(commFileDetail)));
    }

    @Operation(summary = "修改图片prompt可见性")
    @PostMapping("/view-change")
    @Authorization
    public R<Boolean> dealHistoryCommFile(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                          @RequestParam("publicType") @Parameter(description = "公开类型") String publicType,
                                          @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commImgService.changePublicType(publicType, commFileId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "删除社区图片(批量)")
    @PostMapping("/delete-batch")
    @Authorization
    public R<Boolean> deleteCommFileBatch(@RequestBody @Parameter(description = "社区图片id列表") List<String> commFileIds,
                                          @CurrentUser @Parameter(hidden = true) User user) {
        if (CollectionUtils.isEmpty(commFileIds)) {
            return R.fail(400, "Please select the picture to be deleted !");
        }

        if (commFileIds.size() > 40) {
            return R.fail(400, "Please select no more than 40 pictures to be deleted !");
        }
        Boolean result = commImgService.deleteCommFileBatchSegmentation(commFileIds, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "修改图片prompt可见性(批量)")
    @PostMapping("/view-change-batch")
    @Authorization
    public R<Boolean> changePublicTypeBatch(@RequestBody @Parameter(description = "社区图片id列表") CommImgBatch commImgBatch,
                                            @CurrentUser @Parameter(hidden = true) User user) {
        if (CollectionUtils.isEmpty(commImgBatch.getCommFileIds()) || StringUtils.isBlank(commImgBatch.getPublicType())) {
            return R.fail(400, "Please select the picture and public type !");
        }

        if (commImgBatch.getCommFileIds().size() > 40) {
            return R.fail(400, "Please select no more than 40 pictures  !");
        }
        Boolean result = commImgService.changePublicTypeBatchSegmentation(commImgBatch.getPublicType(), commImgBatch.getCommFileIds(), user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

//    @Operation(summary = "处理历史图片")
//    @PostMapping("/deal-history-file")
//    @Deprecated
//    public R<Boolean> dealHistoryCommFile(@RequestParam("tableName") String tableName) {
//        return R.success(commImgService.dealHistoryCommFile(tableName));
//    }

//    @Operation(summary = "对社区图片进行高质量修改")
//    @PostMapping("/add-featured")
//    @Authorization
//    public R<Boolean> addQualityCommFile(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
//                                      @CurrentUser @Parameter(hidden = true) User user) {
//        if(!commonService.judegeTheFeaturedPermissions(user)){
//            return R.fail(400, "No operation permission !");
//        }
//        Boolean result = commImgService.addQualityCommFile(commFileId, user);
//        return result ? R.success(result) : R.fail(1, "Img does not exist");
//    }

    @Operation(summary = "获取社区首页banner图片")
    @GetMapping("/banner-img-list")
    public R<List<BannerImgVO>> getBannerImgList() {
        return R.success(commImgService.getBannerImgList());
    }


    @Operation(summary = "获取社区首页banner图片(ios)")
    @GetMapping("/banner-img-list-ios")
    public R<List<BannerImgIosVO>> getBannerIosImgList() {
        return R.success(commImgService.getBannerIosImgList());
    }
}
