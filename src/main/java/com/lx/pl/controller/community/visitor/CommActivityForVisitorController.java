package com.lx.pl.controller.community.visitor;

import com.lx.pl.db.mysql.community.activity.CommActivityPrizeSettings;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommActivityPrizeSettingsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/17
 * @description
 */
@Tag(name = "社区活动相关接口（游客模式）")
@Slf4j
@RestController
@RequestMapping("/api/comm-activity-for-visitor")
public class CommActivityForVisitorController {
    @Autowired
    private CommActivityPrizeSettingsService commActivityPrizeSettingsService;

    @Operation(summary = "获取奖章列表")
    @PostMapping("/prize-list")
    public R<List<CommActivityPrizeSettings>> listPrize() {
        List<CommActivityPrizeSettings> list = commActivityPrizeSettingsService.list();
        return R.success(list);
    }
}
