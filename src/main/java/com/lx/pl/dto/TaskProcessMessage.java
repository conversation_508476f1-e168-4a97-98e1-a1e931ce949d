package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
public class TaskProcessMessage {

    @Schema(description = "生图prompt_id")
    private String promptId;

    @Schema(description = "java生成的markId")
    private String markId;

    @Schema(description = "当前生图任务执行状态")
    private String status;

    @Schema(description = "错误提示")
    private String info;

    @Schema(description = "当前生图任务排队顺序")
    private Integer index;

    @Schema(description = "任务正向提示词")
    private String prompt;

    private Integer duration;

    @Schema(description = "生图成功后地址")
    private List<ImgUrl> img_urls;

    @Schema(description = "功能类型")
    private String featureName;


    @ToString
    @Data
    public static class ImgUrl {

        @Schema(description = "图片生成后地址")
        private String imgUrl;

        @Schema(description = "缩略图地址")
        private String thumbnailUrl;

        @Schema(description = "高清缩略图路径")
        private String highThumbnailUrl;

        @Schema(description = "小图路径")
        private String miniThumbnailUrl;

        @Schema(description = "30% 高清图")
        private String highMiniUrl;

        @Schema(description = "敏感图片名称")
        private String imgName;

        @Schema(description = "敏感信息")
        private String sensitive;

        @Schema(description = "生成图片的宽")
        private int realWidth;

        @Schema(description = "生成图片的高")
        private int realHeight;

        @Schema(description = "视频时长")
        private Integer duration;

        @Schema(description = "视频文件链接")
        private String videoUrl;
    }
}
