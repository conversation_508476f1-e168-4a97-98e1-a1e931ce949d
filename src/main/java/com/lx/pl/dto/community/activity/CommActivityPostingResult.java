package com.lx.pl.dto.community.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CommActivityPostingResult {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String title;

    /** 活动类型 0.图片  1.视频 2 all */
    @Schema(description = "活动类型 0.图片  1.视频 2 all")
    private Integer type;
    
}