package com.lx.pl.dto.community.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CommActivityResult {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String title;

    /**
     * 活动投稿开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 活动投稿结束时间
     */
    private LocalDateTime postEndTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动标签，JSON数组格式
     */
    private String tags;

    /**
     * 活动图片 URL
     */
    private String cover;

    /**
     * 单用户投稿上限
     */
    private Integer maxSubmissions;

    /**
     * 发布状态：0-未发布，1-已发布
     */
    private Boolean publish;

    /**
     * 奖励状态：0-未发奖励，1-已发奖励
     */
    private Boolean reward;

    /**
     * 活动图片数量
     */
    private Long imageNum;

    @Schema(description = "活动类型 0.图片  1.视频 2 all")
    private Integer type;

}