package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 视频生成API请求参数
 */
@ToString
@Data
public class VideoGenerationParams {

    @Schema(description = "图片URL列表（图生视频时使用）")
    private String img_url;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "反向提示词")
    private String negative_prompt;

    @Schema(description = "模型ID")
    private String model_id;

    @Schema(description = "视频类型：text_to_video（文生视频）或 image_to_video（图生视频）")
    private String video_type;

    @Schema(description = "种子数，-1表示随机")
    private Long seed;

    @Schema(description = "视频宽度（文生视频时使用）")
    private Integer width;

    @Schema(description = "视频高度（文生视频时使用）")
    private Integer height;

    @Schema(description = "回调URL")
    private String callback_url;

    @Schema(description = "服务地址")
    private String address;

    @Schema(description = "是否加速")
    private Boolean accelerated;

    @Schema(description = "标记id")
    private String mark_id;
}
