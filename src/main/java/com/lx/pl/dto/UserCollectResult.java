package com.lx.pl.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserCollectResult {

    /**
     * 用户名称
     */
    private String loginName;

    private String promptId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 收藏夹id
     */
    private Long classifyId;

    /**
     * 文件名称
     */
    private String fileName;


    /**
     * 缩略图名称
     */
    private String thumbnailName;

    /**
     * 高清缩略图名称
     */
    private String highThumbnailName;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    private String highThumbnailUrl;

    private String miniThumbnailUrl;

    /**
     * 敏感信息：涉黄等
     */
    private String sensitiveMessage;

    /**
     * 生成图片的宽
     */
    private int width;

    /**
     * 生成图片的高
     */
    private int height;


    /**
     * 正向提示词
     */
    private String prompt;

    /**
     * 生图信息
     */
    private JsonNode genInfo;

    /**
     * 生图原始操作
     */
    private String originCreate;

    @Schema(description = "是否公开(0:未公开 1:已公开 2:审核中  3.已拒绝)")
    private Integer isPublic;

    @Schema(description = "拒绝内容描述")
    private String rejectionContent;

    @Schema(description = "视频文件链接")
    private String videoUrl;

    @Schema(description = "视频时长")
    private Integer duration;
}
