package com.lx.pl.dto.gemini;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Gemini API响应类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GeminiResponse {

    /**
     * 请求ID
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 任务状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 结果数据
     */
    @JsonProperty("data")
    private Object data;

    /**
     * 错误信息
     */
    @JsonProperty("error")
    private String error;

    /**
     * 详细信息
     */
    @JsonProperty("detail")
    private String detail;

    /**
     * 提交任务响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SubmitTaskResponse {
        /**
         * 请求ID
         */
        @JsonProperty("request_id")
        private String requestId;
    }

    /**
     * 任务结果数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskResult {

        /**
         * 生成的图像列表
         */
        @JsonProperty("images")
        private List<ImageInfo> images;

        /**
         * Gemini的文本描述或响应
         */
        @JsonProperty("description")
        private String description;
    }

    /**
     * fal.ai API直接返回的结果（用于获取任务结果接口）
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FalApiResult {
        /**
         * 生成的图像列表
         */
        @JsonProperty("images")
        private List<ImageInfo> images;

        /**
         * 描述信息
         */
        @JsonProperty("description")
        private String description;
    }

    /**
     * 图像信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageInfo {
        /**
         * 图像URL
         */
        @JsonProperty("url")
        private String url;

        /**
         * 内容类型
         */
        @JsonProperty("content_type")
        private String contentType;

        /**
         * 文件名
         */
        @JsonProperty("file_name")
        private String fileName;

        /**
         * 文件大小
         */
        @JsonProperty("file_size")
        private Integer fileSize;
    }

    /**
     * 任务状态响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskStatusResponse {
        /**
         * 请求ID
         */
        @JsonProperty("request_id")
        private String requestId;

        /**
         * 任务状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 结果数据
         */
        @JsonProperty("data")
        private TaskResult data;

        /**
         * 错误信息
         */
        @JsonProperty("error")
        private String error;

        /**
         * 详细信息
         */
        @JsonProperty("detail")
        private String detail;
    }

    /**
     * fal.ai API错误响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FalErrorResponse {
        /**
         * 错误详情列表
         */
        @JsonProperty("detail")
        private List<FalError> detail;
    }

    /**
     * fal.ai API单个错误对象
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FalError {
        /**
         * 错误位置
         */
        @JsonProperty("loc")
        private List<String> loc;

        /**
         * 人类可读的错误消息
         */
        @JsonProperty("msg")
        private String msg;

        /**
         * 机器可读的错误类型
         */
        @JsonProperty("type")
        private String type;

        /**
         * 错误文档链接
         */
        @JsonProperty("url")
        private String url;

        /**
         * 额外的结构化上下文
         */
        @JsonProperty("ctx")
        private Map<String, Object> ctx;

        /**
         * 导致错误的输入
         */
        @JsonProperty("input")
        private Object input;
    }

    /**
     * 错误响应（兼容旧版本）
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorResponse {
        /**
         * 错误代码
         */
        @JsonProperty("error")
        private String error;

        /**
         * 错误消息
         */
        @JsonProperty("detail")
        private String detail;

        /**
         * 详细信息
         */
        @JsonProperty("details")
        private Map<String, Object> details;
    }
}
