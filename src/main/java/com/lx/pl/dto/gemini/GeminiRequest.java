package com.lx.pl.dto.gemini;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Gemini API请求基础类
 * 基于fal.ai nano-banana API
 *
 * <AUTHOR>
 */
@Data
public class GeminiRequest {

    /**
     * 提示词
     */
    @NotBlank(message = "提示词不能为空")
    @JsonProperty("prompt")
    private String prompt;

    /**
     * 生成图片数量，默认1张
     */
    @JsonProperty("num_images")
    private Integer numImages = 1;

    /**
     * 输出格式：jpeg/png
     */
    @JsonProperty("output_format")
    private String outputFormat = "png";

    /**
     * 同步模式，当为true时，图片将作为data URI返回而不是URL
     */
    @JsonProperty("sync_mode")
    private Boolean syncMode = false;

    /**
     * Nano Banana图像生成请求
     */
    @Data
    public static class NanoBananaRequest extends GeminiRequest {
        // 继承所有基础字段
    }
}
