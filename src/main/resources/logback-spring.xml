<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 日志存放路径 -->
    <property name="log.path" value="/home/<USER>/piclumen/logs"/>
    <property name="info-error.path" value="${log.path}/info-error "/>
    <property name="stripe-pay.path" value="${log.path}/pay-stripe "/>
    <property name="apple-pay.path" value="${log.path}/pay-apple"/>
    <property name="paypal-pay.path" value="${log.path}/pay-paypal"/>
    <property name="google-pay.path" value="${log.path}/pay-google"/>
    <property name="mq.path" value="${log.path}/mq"/>
    <property name="task.path" value="${log.path}/task"/>
    <property name="ws.path" value="${log.path}/ws"/>
    <property name="action.path" value="${log.path}/action"/>
    <property name="info.path" value="${log.path}/info"/>
    <!-- 日志输出格式 -->
    <property name="log.pattern"
              value="[%X{TRACE_ID}] %d{MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--输出格式化-->
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${info-error.path}/sys-info.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${info-error.path}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--输出格式化-->
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${info-error.path}/sys-error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${info-error.path}/sys-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--输出格式化-->
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 定时任务日志输出  -->
    <appender name="schedule-task" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${task.path}/schedule-task.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${task.path}/schedule-task.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--输出格式化-->
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- websocket日志输出  -->
    <appender name="ws-task" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ws.path}/ws-task.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${ws.path}/ws-task.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- rocketmq日志输出  -->
    <appender name="rocketmq-msg" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${mq.path}/rocketmq-msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${mq.path}/rocketmq-msg.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>


    <!-- stripe支付等日志输出  -->
    <appender name="stripe-pay-msg" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${stripe-pay.path}/stripe-pay-msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${stripe-pay.path}/stripe-pay-msg.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>365</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- apple支付等日志输出  -->
    <appender name="apple-pay-msg" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${apple-pay.path}/apple-pay-msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${apple-pay.path}/apple-pay-msg.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>365</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>
    <!-- paypal支付等日志输出  -->
    <appender name="paypal-pay-msg" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${paypal-pay.path}/paypal-pay-msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${paypal-pay.path}/paypal-pay-msg.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>365</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 行为日志输出  -->
    <appender name="action-task" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${action.path}/action-task.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${action.path}/action-task.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--输出格式化-->
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- google 支付等日志输出  -->
    <appender name="google-pay-msg" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${google-pay.path}/google-pay-msg.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${google-pay.path}/google-pay-msg.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>365</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--输出格式化-->
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>
    <!-- 系统模块日志级别控制  -->
    <logger name="com.lx.pl" level="info"/>
    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn"/>


    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
        <appender-ref ref="file_error"/>
    </root>

    <!--定时任务操作日志-->
    <logger name="schedule-task" level="info">
        <appender-ref ref="schedule-task"/>
    </logger>

    <!--websocket操作日志-->
    <logger name="ws-task" level="debug">
        <appender-ref ref="ws-task"/>
    </logger>

    <!--rocketmq-msg操作日志-->
    <logger name="rocketmq-msg" level="info">
        <appender-ref ref="rocketmq-msg"/>
    </logger>

    <logger name="stripe-pay-msg" level="info">
        <appender-ref ref="stripe-pay-msg"/>
    </logger>

    <logger name="apple-pay-msg" level="info">
        <appender-ref ref="apple-pay-msg"/>
    </logger>

    <!--行为日志-->
    <logger name="action-task" level="info">
        <appender-ref ref="action-task"/>
    </logger>

    <logger name="paypal-pay-msg" level="info">
        <appender-ref ref="paypal-pay-msg"/>
    </logger>
    <logger name="google-pay-msg" level="info">
        <appender-ref ref="google-pay-msg"/>
    </logger>
</configuration>