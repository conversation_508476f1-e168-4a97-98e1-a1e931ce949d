<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.pay.common.mapper.PromotionConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lx.pl.pay.common.domain.PromotionConfig">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="coupon_id" property="couponId" />
        <result column="off" property="off" />
        <result column="start_time" property="startTime" />
        <result column="redeem_by" property="redeemBy" />
        <result column="enable" property="enable" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, name, coupon_id, off, start_time, redeem_by, enable, create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据类型获取有效的优惠配置 -->
    <select id="getValidPromotionsByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM promotion_config
        WHERE type = #{type}
        AND enable = 1
        AND (start_time IS NULL OR start_time &lt;= #{currentTime})
        AND (redeem_by IS NULL OR redeem_by &gt;= #{currentTime})
        ORDER BY create_time DESC
    </select>

    <!-- 根据优惠券ID获取配置 -->
    <select id="getByCouponId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM promotion_config
        WHERE coupon_id = #{couponId}
        LIMIT 1
    </select>

    <!-- 获取所有有效的优惠配置 -->
    <select id="getAllValidPromotions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM promotion_config
        WHERE enable = 1
        AND (start_time IS NULL OR start_time &lt;= #{currentTime})
        AND (redeem_by IS NULL OR redeem_by &gt;= #{currentTime})
        ORDER BY type, create_time DESC
    </select>
</mapper>
