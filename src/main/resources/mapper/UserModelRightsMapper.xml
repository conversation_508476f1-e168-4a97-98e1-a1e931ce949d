<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.UserModelRightsMapper">
    <update id="incrPayTrial">
        update user_model_rights
        set used_pay_trial = used_pay_trial + #{modifyCount}
        where user_login_name = #{loginName}
          and model_id = #{modelId}
          and is_deleted = 0
    </update>

    <update id="incrFreeTrial">
        update user_model_rights
        set used_free_trial = used_free_trial + #{modifyCount}
        where user_login_name = #{loginName}
          and model_id = #{modelId}
          and is_deleted = 0
    </update>


    <update id="decrFreeTrial">
            update user_model_rights
            set used_free_trial = used_free_trial - #{modifyCount}
            where user_login_name = #{loginName}
              and model_id = #{modelId}
              and used_free_trial &gt; 0
              and is_deleted = 0
    </update>

    <update id="decrPayTrial">
            update user_model_rights
            set used_pay_trial = used_pay_trial - #{modifyCount}
            where user_login_name = #{loginName}
              and model_id = #{modelId}
              and used_pay_trial &gt; 0
              and is_deleted = 0
    </update>

    <select id="selectUserModelRights" resultType="com.lx.pl.dto.UserModelRightsCacheDTO">
        select id              as id,
               user_id         as userId,
               user_login_name as userLoginName,
               model_id        as modelId,
               used_pay_trial  as usedPayTrial,
               used_free_trial as usedFreeTrial
        from user_model_rights
        where user_login_name = #{loginName}
          and is_deleted = 0
    </select>
</mapper>