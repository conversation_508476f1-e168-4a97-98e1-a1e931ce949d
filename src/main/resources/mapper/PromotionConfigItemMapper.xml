<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.pay.common.mapper.PromotionConfigItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lx.pl.pay.common.domain.PromotionConfigItem">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="price_interval" property="priceInterval" />
        <result column="plan_level" property="planLevel" />
        <result column="product_type" property="productType" />
        <result column="description" property="description" />
        <result column="front_month_price" property="frontMonthPrice" />
        <result column="off" property="off" />
        <result column="front_off" property="frontOff" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, price_interval, plan_level, product_type, description, off, front_off, front_month_price,
        create_by, update_by, create_time, update_time
    </sql>

    <!-- 根据配置ID获取配置项列表 -->
    <select id="getByConfigId" resultType="com.lx.pl.pay.common.dto.PromotionConfigItemDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM promotion_config_item
        WHERE config_id = #{configId}
        ORDER BY id ASC
    </select>

    <!-- 根据产品类型和计划等级获取配置项 -->
    <select id="getByProductTypeAndPlanLevel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM promotion_config_item
        WHERE 1=1
        <if test="productType != null and productType != ''">
            AND product_type = #{productType}
        </if>
        <if test="planLevel != null and planLevel != ''">
            AND plan_level = #{planLevel}
        </if>
        <if test="priceInterval != null and priceInterval != ''">
            AND price_interval = #{priceInterval}
        </if>
        ORDER BY off DESC, id ASC
    </select>

    <!-- 根据配置ID和产品类型获取配置项 -->
    <select id="getByConfigIdAndProductType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM promotion_config_item
        WHERE config_id = #{configId}
        <if test="productType != null and productType != ''">
            AND product_type = #{productType}
        </if>
        ORDER BY off DESC, id ASC
    </select>

</mapper>
